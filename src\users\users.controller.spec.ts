import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { ErrorService } from '../error/error.service';
import { BadgeDto } from './dto/badge.dto';
import { RequestParticipantsDTO, RequestRegisteredDTO } from './dto/users.dto';
import { Types } from 'mongoose';
import { mockPayload } from './mock/mock-values.mock';
import { ComparisonOperator } from './enum/user.enum';

describe('UsersController', () => {
  let controller: UsersController;
  let service: UsersService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: {
            createUser: jest.fn().mockResolvedValue([mockPayload]),
            getUserById: jest.fn().mockResolvedValue(mockPayload),
            getAllUsers: jest.fn().mockResolvedValue([mockPayload]),
            getUserAssignment: jest.fn().mockResolvedValue([mockPayload]),
            fetchAll: jest.fn().mockResolvedValue([mockPayload]),
            getProgramEntities: jest.fn().mockResolvedValue([mockPayload]),
            upsertBadge: jest.fn().mockResolvedValue(mockPayload),
            getProgramParticipants: jest.fn().mockResolvedValue([mockPayload]),
            getRegisteredIds: jest.fn().mockResolvedValue([mockPayload])
          },
        },
        {
          provide: ErrorService,
          useValue: {
            handleError: jest.fn()
          },
        }
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    service = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createUser', () => {
    it('should create a user', async () => {
      const result = await controller.createUser([mockPayload]);
      
      expect(result).toBeDefined();
      expect(service.createUser).toHaveBeenCalledWith([mockPayload]);
      expect(result).toEqual([mockPayload]);
    });
  });

  describe('getUserById', () => {
    it('should get a user by id with last_sync_time', async () => {
      const userId = 'test-id';
      const lastSyncTime = '2023-01-01';
      const result = await controller.getUserById(userId, lastSyncTime);
      
      expect(service.getUserById).toHaveBeenCalledWith(userId, lastSyncTime);
      expect(result).toEqual(mockPayload);
    });

    it('should get a user by id without last_sync_time', async () => {
      const userId = 'test-id';
      const result = await controller.getUserById(userId);
      
      expect(service.getUserById).toHaveBeenCalledWith(userId, undefined);
      expect(result).toEqual(mockPayload);
    });
  });

  describe('getAllUsers', () => {
    it('should get all users for an organization', async () => {
      const orgId = 'org-id';
      const lastSyncTime = '2023-01-01';
      const result = await controller.getAllUsers(orgId, lastSyncTime);
      
      expect(service.getAllUsers).toHaveBeenCalledWith(orgId, lastSyncTime);
      expect(result).toEqual([mockPayload]);
    });

    it('should get all users without last sync time', async () => {
      const orgId = 'org-id';
      const result = await controller.getAllUsers(orgId);
      
      expect(service.getAllUsers).toHaveBeenCalledWith(orgId, undefined);
      expect(result).toEqual([mockPayload]);
    });
  });

  describe('getAssignedUser', () => {
    it('should get assigned users with all parameters', async () => {
      const headers = {
        'user-id': 'user-123',
        'tenant-id': 'tenant-123'
      };
      const query = {
        program_id: 'program-123',
        organization_id: 'org-123',
        role_id: 'role-123',
        last_sync_time: '2023-01-01'
      };

      const result = await controller.getAssignedUser(
        headers['user-id'],
        headers['tenant-id'],
        query.role_id,
        query.program_id,
        query.last_sync_time
      );

      expect(service.getUserAssignment).toHaveBeenCalledWith(
        headers['user-id'],
        headers['tenant-id'],
        query.role_id,
        query.program_id,
        query.last_sync_time
      );
      expect(result).toEqual([mockPayload]);
    });

    it('should get assigned users without last sync time', async () => {
      const headers = {
        'user-id': 'user-123',
        'tenant-id': 'tenant-123'
      };
      const query = {
        program_id: 'program-123',
        organization_id: 'org-123',
        role_id: 'role-123'
      };

      const result = await controller.getAssignedUser(
        headers['user-id'],
        headers['tenant-id'],
        query.role_id,
        query.program_id
      );

      expect(service.getUserAssignment).toHaveBeenCalledWith(
        headers['user-id'],
        headers['tenant-id'],
        query.role_id,
        query.program_id,
        undefined
      );
      expect(result).toEqual([mockPayload]);
    });
  });

  describe('fetchAll', () => {
    it('should fetch all users', async () => {
      const result = await controller.fetchAll();
      
      expect(service.fetchAll).toHaveBeenCalled();
      expect(result).toEqual([mockPayload]);
    });
  });

  describe('getProgramEntities', () => {
    it('should get program entities', async () => {
      const programId = 'program-123';
      const organizationId = 'org-123';
      const result = await controller.getProgramEntities(programId, organizationId);
      
      expect(service.getProgramEntities).toHaveBeenCalledWith(programId, organizationId);
      expect(result).toEqual([mockPayload]);
    });
  });

  describe('upsertBadge', () => {
    it('should update or insert badge', async () => {
      const badgePayload: BadgeDto = {
        id: 'badge-123',
        badge_id: [new Types.ObjectId('507f1f77bcf86cd799439011')],
        organization_id: new Types.ObjectId('507f1f77bcf86cd799439012'),
      };
      const result = await controller.upsertBadge(badgePayload);
      
      expect(service.upsertBadge).toHaveBeenCalledWith(badgePayload);
      expect(result).toEqual(mockPayload);
    });
  });

  describe('getParticipantUserIds', () => {
    it('should get participant user ids', async () => {
      const participantsPayload: RequestParticipantsDTO[] = [{
        program_id: new Types.ObjectId('507f1f77bcf86cd799439011'),
        entity_role_id: new Types.ObjectId('507f1f77bcf86cd799439012'),
        organization_id: new Types.ObjectId('507f1f77bcf86cd799439013'),
        entity_ids: []
      }];
      const result = await controller.getParticipantUserIds(participantsPayload);
      
      expect(service.getProgramParticipants).toHaveBeenCalledWith(participantsPayload);
      expect(result).toEqual([mockPayload]);
    });
  });

  describe('getRegisteredUserIds', () => {
    it('should get registered user ids', async () => {
      const registeredPayload: RequestRegisteredDTO[] = [{
        onboarding_date: '2023-01-01',
        comparison_operator: ComparisonOperator.EARLIER_THAN,
        organization_id: new Types.ObjectId('507f1f77bcf86cd799439011'),
        entity_ids: []
      }];
      const result = await controller.getRegisteredUserIds(registeredPayload);
      
      expect(service.getRegisteredIds).toHaveBeenCalledWith(registeredPayload);
      expect(result).toEqual([mockPayload]);
    });
  });
});
