import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { ErrorService } from '../error/error.service';
import { PayloadUtils } from '../utils/payload.utils';
import { Types } from 'mongoose';
import { CreateUserDto } from './dto/users.dto';
import { EntityAssignmentService } from '../entity-assignment/entity-assignment.service';
import { EntityPortfolioService } from '../entity-portfolio/entity-portfolio.service';
import { CaseConverterUtil } from '../utils/case-converter.util';
import { AssetService } from '../asset/asset.service';
import { getModelToken } from '@nestjs/mongoose';
import { User } from './schema/user.schema';
import { ScreenDto } from '../users/dto/screen.dto';
import { BadgeDto } from './dto/badge.dto';
import axios from 'axios';
import { ComparisonOperator } from './enum/user.enum';
import { mockAssets, mockBadgePayload, mockEvent, mockOrgId, mockPayload, mockProgramId, mockTransformedPayload, mockTransformedPayloadUpdate, mockUsers } from './mock/mock-values.mock';

const mockRoleId = new Types.ObjectId();
const mockEntityId = new Types.ObjectId();


describe('UsersService', () => {
  let service: UsersService;
  let mockUserModel: any;
  let mockErrorService: any;
  let mockPayloadUtils: any;
  let mockEntityAssignmentService: any;
  let mockEntityPortfolioService: any;
  let mockCaseConverterUtil: any;
  let mockAssetService: any;

  beforeEach(async () => {
    jest.clearAllMocks();

    mockUserModel = {
      find: jest.fn(),
      findOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
      create: jest.fn(),
      findById: jest.fn(),
    };

    mockErrorService = {
      serverError: jest.fn(),
      badRequestError: jest.fn(),
      notFoundError: jest.fn(),
    };

    mockPayloadUtils = {
      transformPayload: jest.fn(),
      generateId: jest.fn(),
    };

    mockCaseConverterUtil = {
      convertKeysToSnakeCase: jest.fn(),
      convertKeysToCamelCase: jest.fn(),
    };

    mockEntityPortfolioService = {
      getPortfolio: jest.fn(),
      getStaffPortfolio: jest.fn(),
    };

    mockAssetService = {
      getProgramAssets: jest.fn(),
    };

    const module = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
        {
          provide: ErrorService,
          useValue: mockErrorService,
        },
        {
          provide: PayloadUtils,
          useValue: mockPayloadUtils,
        },
        {
          provide: EntityAssignmentService,
          useValue: mockEntityAssignmentService,
        },
        {
          provide: EntityPortfolioService,
          useValue: mockEntityPortfolioService,
        },
        {
          provide: CaseConverterUtil,
          useValue: mockCaseConverterUtil,
        },
        {
          provide: AssetService,
          useValue: mockAssetService,
        },
      ],
    }).compile();

    service = await module.resolve(UsersService);
    jest.spyOn(service as any, 'loadCronStatus').mockImplementation(() => {});
    jest.spyOn(service as any, 'saveCronStatus').mockImplementation(() => Promise.resolve());
    service['cronStatus'] = {
      last_sync_time: new Date('2025-06-30T10:47:20.214Z')
    };
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('fetchAll', () => {
    it('should return all users', async () => {
      mockUserModel.find.mockResolvedValue(mockUsers);

      const result = await service.fetchAll();

      expect(mockUserModel.find).toHaveBeenCalledWith({ delete_flag: 0 });
      expect(result).toEqual(mockUsers);
    });

    it('should handle errors', async () => {
      mockUserModel.find.mockRejectedValue(new Error('Something went wrong'));

      const result = await service.fetchAll();

      expect(result).toStrictEqual([]);
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('getProgramEntities', () => {
    it('should return combined users and assets for a program', async () => {
      mockUserModel.find.mockResolvedValue(mockUsers);
      mockAssetService.getProgramAssets.mockResolvedValue(mockAssets);

      const result = await service.getProgramEntities(mockProgramId, mockOrgId);

      expect(mockUserModel.find).toHaveBeenCalledWith({
        program_ids: { $in: [new Types.ObjectId(mockProgramId)] },
        organization_id: new Types.ObjectId(mockOrgId),
        delete_flag: 0,
      });
      expect(mockAssetService.getProgramAssets).toHaveBeenCalledWith(
        mockProgramId,
        mockOrgId
      );
      expect(result).toEqual({
        message: 'Program entities gotten successfully',
        status: 'success',
        data: [...mockUsers, ...mockAssets]
      });
    });

    it('should handle empty results', async () => {
      mockUserModel.find.mockResolvedValue([]);
      mockAssetService.getProgramAssets.mockResolvedValue([]);

      const result = await service.getProgramEntities(mockProgramId, mockOrgId);

      expect(result).toEqual({
        message: 'Program entities gotten successfully',
        status: 'success',
        data: []
      });
    });

    it('should handle null assets result', async () => {
      mockUserModel.find.mockResolvedValue(mockUsers);
      mockAssetService.getProgramAssets.mockResolvedValue(null);

      const result = await service.getProgramEntities(mockProgramId, mockOrgId);

      expect(result).toEqual({
        message: 'Program entities gotten successfully',
        status: 'success',
        data: [...mockUsers]
      });
    });

    it('should handle errors', async () => {
      mockUserModel.find.mockRejectedValue(new Error('Database error'));

      const result = await service.getProgramEntities(mockProgramId, mockOrgId);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('createUser', () => {
    const createdUser = { ...mockTransformedPayload };

    beforeEach(() => {
      mockPayloadUtils.transformPayload.mockReturnValue(mockTransformedPayload);
    });

    it('should create a new user when user does not exist', async () => {
      mockUserModel.findOne.mockResolvedValue(null);
      mockUserModel.create.mockResolvedValue(createdUser);

      const result = await service.createUser([mockPayload]);

      expect(mockPayloadUtils.transformPayload).toHaveBeenCalledWith(expect.objectContaining(mockPayload));
      expect(mockUserModel.findOne).toHaveBeenCalledWith({ id: 'test-id', delete_flag: 0 });
      expect(mockUserModel.create).toHaveBeenCalledWith(expect.objectContaining({
        ...mockTransformedPayload,
        organization_id: expect.any(Types.ObjectId),
        entity: expect.arrayContaining([expect.any(Types.ObjectId)]),
        program_ids: expect.arrayContaining([expect.any(Types.ObjectId)]),
        role: expect.any(Types.ObjectId)
      }));
      expect(result).toEqual([createdUser]);
    });

    it('should update existing user when user exists', async () => {
      const existingUser = { ...createdUser };
      const updatedUser = { ...existingUser };

      mockUserModel.findOne.mockResolvedValue(existingUser);
      mockUserModel.findOneAndUpdate.mockResolvedValue(updatedUser);

      const result = await service.createUser([mockPayload]);

      expect(mockPayloadUtils.transformPayload).toHaveBeenCalledWith(expect.objectContaining(mockPayload));
      expect(mockUserModel.findOne).toHaveBeenCalledWith({ id: 'test-id', delete_flag: 0 });
      expect(mockUserModel.findOneAndUpdate).toHaveBeenCalledWith(
        { id: 'test-id', delete_flag: 0 },
        expect.objectContaining({
          ...mockTransformedPayload,
          organization_id: expect.any(Types.ObjectId),
          entity: expect.arrayContaining([expect.any(Types.ObjectId)]),
          program_ids: expect.arrayContaining([expect.any(Types.ObjectId)]),
          role: expect.any(Types.ObjectId)
        }),
        { new: true, upsert: true, setDefaultsOnInsert: true }
      );
      expect(result).toEqual([updatedUser]);
    });

    it('should handle validation errors during creation', async () => {
      mockUserModel.findOne.mockResolvedValue(null);
      mockUserModel.create.mockRejectedValue(new Error('Validation error'));

      const result = await service.createUser([mockPayload]);
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should handle findOneAndUpdate errors', async () => {
      const existingUser = { ...createdUser };
      mockUserModel.findOne.mockResolvedValue(existingUser);
      mockUserModel.findOneAndUpdate.mockRejectedValue(new Error('Update error'));

      const result = await service.createUser([mockPayload]);
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should handle bulk user creation', async () => {
      const users = [
        { ...mockPayload, id: 'user-1' },
        { ...mockPayload, id: 'user-2' }
      ];
      const transformedUsers = users.map(user => ({ ...user, transformed: true }));
      const createdUsers = transformedUsers.map(user => ({ ...user, created: true }));

      mockPayloadUtils.transformPayload.mockReturnValueOnce(transformedUsers[0]);
      mockPayloadUtils.transformPayload.mockReturnValueOnce(transformedUsers[1]);
      mockUserModel.findOne.mockResolvedValue(null);
      mockUserModel.create.mockResolvedValueOnce(createdUsers[0]);
      mockUserModel.create.mockResolvedValueOnce(createdUsers[1]);

      const result = await service.createUser(users);

      expect(result).toHaveLength(2);
      expect(result).toEqual(createdUsers);
      expect(mockUserModel.create).toHaveBeenCalledTimes(2);
    });

    it('should handle missing required fields', async () => {
      const invalidPayload: CreateUserDto = {
        performer_id: 'test-id',
        organization_id: new Types.ObjectId(),
        entity: [new Types.ObjectId()],
        role: new Types.ObjectId(),
        program_ids: [new Types.ObjectId()],
        unique_entity_id: 'test-unique-id',
        response: [],
        response_id: 'test-response-id',
        target_id: 'test-target-id',
        home_office: {
          id: 'test-id',
          code: 'test-code',
          name: 'test-name',
          level: 'test-level'
        }
      };
      
      mockPayloadUtils.transformPayload.mockReturnValue(invalidPayload);
      mockUserModel.create.mockRejectedValue(new Error('Validation failed'));

      const result = await service.createUser([invalidPayload]);
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should update existing user when id exists and user is found', async () => {
      const payloadWithId = {
        ...mockPayload,
        id: 'existing-id'
      } as CreateUserDto;
      
      const existingUser = { id: 'existing-id' };
      mockUserModel.findOne.mockResolvedValue(existingUser);
      mockPayloadUtils.transformPayload.mockReturnValue(payloadWithId);
      mockUserModel.findOneAndUpdate.mockResolvedValue(payloadWithId);

      const result = await service.createUser([payloadWithId]);

      expect(mockUserModel.findOne).toHaveBeenCalledWith({ 
        id: 'existing-id', 
        delete_flag: 0 
      });
      expect(mockUserModel.findOneAndUpdate).toHaveBeenCalledWith(
        { id: 'existing-id', delete_flag: 0 },
        payloadWithId,
        { new: true, upsert: true, setDefaultsOnInsert: true }
      );
      expect(result).toEqual([payloadWithId]);
    });

    it('should create new user when unique_entity_id exists and data is true', async () => {
      const roleId = new Types.ObjectId();
      const payloadWithUniqueId = {
        ...mockPayload,
        unique_entity_id: 'test-unique-id',
        role: roleId,
        entity: [roleId]
      };
      const transformedPayload = {
        ...payloadWithUniqueId,
        id: 'test-unique-id'
      };
      const updatedUser = { ...transformedPayload };

      mockPayloadUtils.transformPayload.mockReturnValue(transformedPayload);
      mockUserModel.findOne.mockResolvedValue({ id: 'test-unique-id' }); // This triggers the data condition
      mockUserModel.findOneAndUpdate.mockResolvedValue(updatedUser);

      const result = await service.createUser([payloadWithUniqueId]);

      expect(mockUserModel.findOne).toHaveBeenCalledWith(
        expect.objectContaining({
          delete_flag: 0
        })
      );
      expect(mockUserModel.findOneAndUpdate).toHaveBeenCalledWith(
        { id: 'test-unique-id', delete_flag: 0 },
        expect.objectContaining({
          unique_entity_id: 'test-unique-id',
          id: 'test-unique-id'
        }),
        { new: true, upsert: true, setDefaultsOnInsert: true }
      );
      expect(result).toEqual([updatedUser]);
    });

    it('should create new user with direct transformedPayload', async () => {
      const simplePayload = {
        ...mockPayload,
        id: 'direct-test-id'
      };
      const transformedPayload = {
        ...simplePayload,
        _id: 'direct-test-id'
      };
      const newUser = { ...transformedPayload };

      mockPayloadUtils.transformPayload.mockReturnValue(transformedPayload);
      mockUserModel.findOne.mockResolvedValue(null);
      mockUserModel.create.mockResolvedValue(newUser);

      const result = await service.createUser([simplePayload]);

      expect(mockUserModel.create).toHaveBeenCalledWith(transformedPayload);
      expect(result).toEqual([newUser]);
    });

    it('should create new user when role matches entity and unique_entity_id exists', async () => {
      const roleId = new Types.ObjectId();
      const payloadWithMatchingRole = new CreateUserDto();
      Object.assign(payloadWithMatchingRole, {
        ...mockPayload,
        role: roleId,
        entity: [roleId],
        unique_entity_id: 'test-entity-id'
      });
      mockUserModel.findOne.mockResolvedValueOnce(null);
      mockUserModel.create.mockResolvedValueOnce(payloadWithMatchingRole);
      mockPayloadUtils.transformPayload.mockReturnValueOnce(payloadWithMatchingRole);

      const result = await service.createUser([payloadWithMatchingRole]);

      expect(mockUserModel.create).toHaveBeenCalled();
      expect(result).toEqual([payloadWithMatchingRole]);
    });

    it('should throw bad request error when unique_entity_id is missing for matching role', async () => {
      const testPayload = new CreateUserDto();
      Object.assign(testPayload, {
        ...mockPayload,
        unique_entity_id: ''
      });

      try {
        await service.createUser([testPayload] as CreateUserDto[]);
      } catch (error) {
        expect(mockErrorService.badRequestError).toHaveBeenCalledWith('user with unique_entity_id is required');
      }
    });

    it('should handle server errors', async () => {
      const error = new Error('Something went wrong');
      mockUserModel.findOne.mockRejectedValueOnce(error);

      const result = await service.createUser([mockPayload]);
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should handle database errors', async () => {
      const error = new Error('Something went wrong');
      mockUserModel.findOne.mockRejectedValue(error);
      mockPayloadUtils.transformPayload.mockReturnValue(mockPayload);

      const result = await service.createUser([mockPayload]);
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should update user when response_id exists', async () => {
      const mockUser = { id: 'test-id', ...mockTransformedPayloadUpdate };
      
      jest.spyOn(service['payloadUtils'], 'transformPayload').mockReturnValue(mockTransformedPayloadUpdate);
      jest.spyOn(service['userModel'], 'findOne').mockResolvedValue(mockUser as any);
      jest.spyOn(service['userModel'], 'findOneAndUpdate').mockResolvedValue(mockUser as any);

      const result = await service.createUser([mockPayload]);

      expect(service['userModel'].findOneAndUpdate).toHaveBeenCalledWith(
        { response_id: mockPayload.response_id, delete_flag: 0 },
        mockTransformedPayloadUpdate,
        { new: true, upsert: true, setDefaultsOnInsert: true }
      );
      expect(result).toStrictEqual([mockUser]);
    });

    it('should create new user when no matching criteria found', async () => {
      const mockUser = { ...mockTransformedPayload };
      
      jest.spyOn(service['payloadUtils'], 'transformPayload').mockReturnValue(mockTransformedPayload);
      jest.spyOn(service['userModel'], 'findOne').mockResolvedValue(null);
      jest.spyOn(service['userModel'], 'create').mockResolvedValue(mockUser as any);

      const result = await service.createUser([mockPayload]);

      expect(service['userModel'].create).toHaveBeenCalledWith(mockTransformedPayload);
      expect(result).toStrictEqual([mockUser]);
    });

    it('should create a user successfully', async () => {
      const transformedPayload = { ...mockPayload, transformed: true };
      mockPayloadUtils.transformPayload.mockReturnValue(transformedPayload);
      mockUserModel.findOne.mockResolvedValue(null);
      mockUserModel.create.mockResolvedValue({
        ...transformedPayload,
        _id: 'test-id',
        id: 'test-id'
      });

      const result = await service.createUser([mockPayload]);

      expect(result).toBeDefined();
      expect(mockPayloadUtils.transformPayload).toHaveBeenCalledWith(expect.objectContaining({
        ...mockPayload,
        home_office: {}
      }));
      expect(mockUserModel.create).toHaveBeenCalledWith(transformedPayload);
    });

  });

  describe('getUserById', () => {
    it('should return user when found', async () => {
      mockUserModel.findOne.mockResolvedValue(mockUsers);

      const result = await service.getUserById('test-id');
      
      expect(result).toEqual(mockUsers);
      expect(mockUserModel.findOne).toHaveBeenCalledWith({ 
        id: 'test-id', 
        delete_flag: 0,
        updatedAt: { $gte: expect.any(Date) }
      });
    });

    it('should return null when user not found', async () => {
      mockUserModel.findOne.mockResolvedValue(null);
      
      const result = await service.getUserById('non-existent-id');
      
      expect(result).toBeNull();
      expect(mockUserModel.findOne).toHaveBeenCalledWith({ 
        id: 'non-existent-id', 
        delete_flag: 0,
        updatedAt: { $gte: expect.any(Date) }
      });
    });

    it('should handle errors', async () => {
      const error = new Error('Something went wrong');
      mockUserModel.findOne.mockRejectedValue(error);
      
      const result = await service.getUserById('test-id');
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('getAllUsers', () => {
    it('should return users for organization', async () => {
      mockUserModel.find.mockResolvedValue(mockUsers);
      
      const result = await service.getAllUsers('507f1f77bcf86cd799439011');
      
      expect(result).toEqual(mockUsers);
      expect(mockUserModel.find).toHaveBeenCalledWith({ 
        organization_id: new Types.ObjectId('507f1f77bcf86cd799439011'),
        delete_flag: 0,
        updatedAt: { $gte: expect.any(Date) }
      });
    });

    it('should handle errors', async () => {
      const error = new Error('Something went wrong');
      mockUserModel.find.mockRejectedValue(error);

      const result = await service.getAllUsers('507f1f77bcf86cd799439011');
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('getUserAssignment', () => {
    it('should return empty array when no assignment data found', async () => {
      mockEntityPortfolioService.getStaffPortfolio.mockResolvedValueOnce(null);
      
      const result = await service.getUserAssignment('user-id', 'org-id', 'role-id', 'program-id');
      
      expect(result).toStrictEqual([]);
    });

    it('should return users when assignment data exists', async () => {
      mockEntityPortfolioService.getStaffPortfolio.mockResolvedValueOnce({
        data: [{
          portfolio: [{ id: 'test-id' }]
        }]
      });
      mockUserModel.find.mockResolvedValueOnce(mockUsers);

      const result = await service.getUserAssignment('user-id', 'org-id', 'role-id', 'program-id');

      expect(result).toEqual({
        message: 'users gotten successfully',
        status: 'success',
        data: mockUsers
      });
    });

    it('should handle errors in getUserAssignment', async () => {
      const error = new Error('Something went wrong');
      mockEntityPortfolioService.getStaffPortfolio.mockRejectedValue(error);

      const result = await service.getUserAssignment('user-id', 'org-id', 'role-id', 'program-id');
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('upsertBadge', () => {
    it('should update user badge successfully', async () => {
      const mockUser = {
        id: 'test-user-id',
        badge_id: [new Types.ObjectId('507f1f77bcf86cd799439013')],
        save: jest.fn().mockResolvedValue({
          id: 'test-user-id',
          badge_id: [new Types.ObjectId('507f1f77bcf86cd799439012')]
        })
      };
      mockUserModel.findOne.mockResolvedValue(mockUser);

      const result = await service.upsertBadge(mockBadgePayload);

      expect(mockUserModel.findOne).toHaveBeenCalledWith({
        id: mockBadgePayload.id,
        organization_id: mockBadgePayload.organization_id
      });
      expect(mockUser.badge_id).toEqual(mockBadgePayload.badge_id);
      expect(mockUser.save).toHaveBeenCalled();
      expect(result).toMatchObject({
        message: 'Badge updated successfully',
        status: 'success',
        data: {
          id: 'test-user-id',
          badge_id: [new Types.ObjectId('507f1f77bcf86cd799439012')]
        }
      });
    });

    it('should handle user not found', async () => {
      mockUserModel.findOne.mockResolvedValue(null);
      mockErrorService.notFoundError.mockReturnValue('User not found error');

      const result = await service.upsertBadge(mockBadgePayload);

      expect(mockUserModel.findOne).toHaveBeenCalledWith({
        id: mockBadgePayload.id,
        organization_id: mockBadgePayload.organization_id
      });
      expect(mockErrorService.notFoundError).toHaveBeenCalledWith('User not found');
      expect(result).toBe('User not found error');
    });

    it('should handle errors', async () => {
      mockUserModel.findOne.mockRejectedValue(new Error('Database error'));

      const result = await service.upsertBadge(mockBadgePayload);

      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
      expect(result).toStrictEqual([]);
    });
  });

  describe('createEvent', () => {

    beforeEach(() => {
      process.env.BADGE_ASSIGNMENT_SERVICE_BASE_URL = 'http://badge-service';
    });

    it('should successfully create an event', async () => {
      const mockResponse = {
        data: {
          message: 'Event created successfully',
          status: 'success'
        }
      };
      jest.spyOn(axios, 'post').mockResolvedValue(mockResponse);

      const result = await service.createEvent(mockEvent);

      expect(axios.post).toHaveBeenCalledWith(
        'http://badge-service/events',
        mockEvent
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle errors', async () => {
      const mockError = new Error('Network error');
      jest.spyOn(axios, 'post').mockRejectedValue(mockError);
      const consoleSpy = jest.spyOn(console, 'log');

      const result = await service.createEvent(mockEvent);

      expect(axios.post).toHaveBeenCalledWith(
        'http://badge-service/events',
        mockEvent
      );
      expect(consoleSpy).toHaveBeenCalledWith('Error triggering event is', 'Network error');
      expect(result).toBeUndefined();
    });
  });

  describe('getRegisteredUsers', () => {
    it('should fetch active users since last sync time', async () => {
      const lastSyncTime = new Date('2025-06-30T11:00:00.000Z');
      const mockResponse = {
        data: {
          data: [{ id: 'user1', updated_at: '2025-06-30T11:30:00.000Z' }]
        }
      };

      jest.spyOn(axios, 'get').mockResolvedValue(mockResponse);

      const result = await service.getRegisteredUsers(lastSyncTime);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${process.env.IAM_SERVICE_BASE_URL}/users/active?last_sync_time=${lastSyncTime.toISOString()}`
      );
      expect(result).toEqual(mockResponse.data.data);
    });

    it('should handle API errors and return undefined', async () => {
      const lastSyncTime = new Date('2025-06-30T11:00:00.000Z');
      const mockError = new Error('Something went wrong');

      jest.spyOn(axios, 'get').mockRejectedValue(mockError);
      jest.spyOn(service['errorService'], 'serverError').mockImplementation();
      jest.spyOn(console, 'log').mockImplementation();

      const result = await service.getRegisteredUsers(lastSyncTime);

      expect(result).toStrictEqual([]);
      expect(console.log).toHaveBeenCalledWith(mockError.message);
      expect(service['errorService'].serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('getProgramParticipants', () => {
    const mockPayload = [
      {
        entity_role_id: new Types.ObjectId(),
        program_id: new Types.ObjectId(),
        organization_id: new Types.ObjectId(),
        entity_ids: [],
        comparison_operator: ComparisonOperator.EARLIER_THAN
      }
    ];

    it('should return participants successfully', async () => {
      const mockUsers = [{ id: 'user1' }, { id: 'user2' }];
      mockUserModel.find.mockResolvedValue(mockUsers);

      const result = await service.getProgramParticipants(mockPayload);

      expect(mockUserModel.find).toHaveBeenCalledWith(
        {
          role_ids: { $in: [mockPayload[0].entity_role_id] },
          program_ids: { $in: [mockPayload[0].program_id] },
          organization_id: mockPayload[0].organization_id,
        },
        { id: 1, _id: 0 }
      );
      expect(result).toEqual({
        message: 'Participants gotten successfully',
        status: 'success',
        data: [{
          ...mockPayload[0],
          entity_ids: ['user1', 'user2']
        }]
      });
    });

    it('should handle empty results', async () => {
      mockUserModel.find.mockResolvedValue([]);

      const result = await service.getProgramParticipants(mockPayload);

      expect(result).toEqual({
        message: 'Participants gotten successfully',
        status: 'success',
        data: [{
          ...mockPayload[0],
          entity_ids: []
        }]
      });
    });

    it('should handle errors', async () => {
      mockUserModel.find.mockRejectedValue(new Error('Database error'));

      const result = await service.getProgramParticipants(mockPayload);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('getRegisteredIds', () => {
    const mockDate = '2023-01-01T00:00:00.000Z';
    const mockPayload = [
      {
        onboarding_date: mockDate,
        comparison_operator: ComparisonOperator.EARLIER_THAN,
        organization_id: new Types.ObjectId(),
        entity_ids: []
      }
    ];

    it('should return registered ids for EARLIER_THAN operator', async () => {
      const mockUsers = [{ id: 'user1' }, { id: 'user2' }];
      mockUserModel.find.mockResolvedValue(mockUsers);

      const result = await service.getRegisteredIds(mockPayload);

      expect(mockUserModel.find).toHaveBeenCalledWith(
        { createdAt: { $lt: new Date(mockDate) } },
        { id: 1, _id: 0 }
      );
      expect(result).toEqual({
        message: 'Registered entities gotten successfully',
        status: 'success',
        data: [{
          ...mockPayload[0],
          entity_ids: ['user1', 'user2']
        }]
      });
    });

    it('should return registered ids for LATER_THAN operator', async () => {
      const laterThanPayload = [{
        onboarding_date: mockDate,
        comparison_operator: ComparisonOperator.LATER_THAN,
        organization_id: new Types.ObjectId(),
        entity_ids: []
      }];
      const mockUsers = [{ id: 'user3' }];
      mockUserModel.find.mockResolvedValue(mockUsers);

      const result = await service.getRegisteredIds(laterThanPayload);

      expect(mockUserModel.find).toHaveBeenCalledWith(
        { createdAt: { $gt: new Date(mockDate) } },
        { id: 1, _id: 0 }
      );
      expect(result).toEqual({
        message: 'Registered entities gotten successfully',
        status: 'success',
        data: [{
          ...laterThanPayload[0],
          entity_ids: ['user3']
        }]
      });
    });

    it('should handle empty results', async () => {
      mockUserModel.find.mockResolvedValue([]);

      const result = await service.getRegisteredIds(mockPayload);

      expect(result).toEqual({
        message: 'Registered entities gotten successfully',
        status: 'success',
        data: [{
          ...mockPayload[0],
          entity_ids: []
        }]
      });
    });

    it('should handle errors', async () => {
      mockUserModel.find.mockRejectedValue(new Error('Database error'));

      const result = await service.getRegisteredIds(mockPayload);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('migrateUsers', () => {
    beforeEach(() => {
      service['cronStatus'] = {
        last_sync_time: new Date('2025-06-30T08:00:00Z')
      };
      mockCaseConverterUtil.convertKeysToSnakeCase.mockImplementation(obj => obj);
      jest.spyOn(console, 'log');
    });

    it('should migrate users successfully and create events', async () => {
      const currentTime = new Date('2025-06-30T11:49:41.000Z');
      const mockDate = jest.spyOn(global, 'Date').mockImplementation(() => currentTime);
      service['cronStatus'] = { last_sync_time: currentTime };
      
      const mockExistingUser = {
        id: 'user1',
        program_ids: ['program1'],
        role_ids: ['role1'],
        organization_id: 'org1',
        updated_at: currentTime
      };

      const mockUpdatedUser = {
        ...mockExistingUser,
        program_ids: ['program1', 'program2'],
        role_ids: ['role1', 'role2']
      };

      jest.spyOn(service, 'getRegisteredUsers').mockResolvedValue([mockUpdatedUser]);
      mockUserModel.findOne.mockResolvedValueOnce(mockExistingUser);
      mockUserModel.findOneAndUpdate.mockResolvedValue(mockUpdatedUser);
      jest.spyOn(service as any, 'saveCronStatus').mockImplementation(() => Promise.resolve());
      jest.spyOn(service, 'createEvent').mockResolvedValue(undefined);

      await service.migrateUsers();

      expect(service.getRegisteredUsers).toHaveBeenCalledWith(currentTime);
      expect(mockUserModel.findOne).toHaveBeenCalledTimes(1);
      expect(mockUserModel.findOneAndUpdate).toHaveBeenCalledTimes(1);
      expect(service.createEvent).toHaveBeenCalledWith([]);
      expect(console.log).toHaveBeenCalledWith('user migration starting');
      expect(console.log).toHaveBeenCalledWith('User migration completed.');
      expect(console.log).toHaveBeenCalledWith('Total Events triggered:', 0);
      expect(service['cronStatus'].last_sync_time).toEqual(currentTime);
      mockDate.mockRestore();
    });

    it('should create registration events for new users', async () => {
      const currentTime = new Date('2025-06-30T11:49:41.000Z');
      const mockDate = jest.spyOn(global, 'Date').mockImplementation(() => currentTime);
      service['cronStatus'] = { last_sync_time: currentTime };
      
      const mockNewUser = {
        id: 'newUser1',
        tenantId: 'org1',
        program: [
          {
            programId: 'program1',
            role: { id: 'role1' }
          }
        ],
        updated_at: new Date('2025-06-30T11:50:00.000Z')
      };

      jest.spyOn(service, 'getRegisteredUsers').mockResolvedValue([mockNewUser]);
      mockUserModel.findOne.mockResolvedValueOnce(null);
      mockUserModel.findOneAndUpdate.mockResolvedValue({
        ...mockNewUser,
        organization_id: 'org1',
        program_ids: ['program1'],
        role_ids: ['role1']
      });
      jest.spyOn(service as any, 'saveCronStatus').mockImplementation(() => Promise.resolve());
      jest.spyOn(service, 'createEvent').mockResolvedValue(undefined);

      await service.migrateUsers();

      expect(service.createEvent).toHaveBeenCalledWith([{
        organization_id: 'org1',
        entity_id: 'newUser1',
        program_id: 'program1',
        entity_role_id: 'role1',
        event_type: 'date_of_registration',
        program_participated_id: 'program1',
        registration_date: currentTime.toISOString()
      },
      {
        organization_id: 'org1',
        entity_id: 'newUser1',
        program_id: 'program1',
        entity_role_id: 'role1',
        event_type: 'program_participation',
        program_participated_id: 'program1'
      }]);
      expect(console.log).toHaveBeenCalledWith('Inserted users:', 1, [expect.objectContaining({
        id: 'newUser1',
        organization_id: 'org1',
        program_ids: ['program1'],
        role_ids: ['role1']
      })]);
      mockDate.mockRestore();
    });

    it('should skip users not updated since last sync', async () => {
      const currentTime = new Date('2025-06-30T11:49:41.000Z');
      const lastSyncTime = new Date('2025-06-30T11:00:00.000Z');
      const mockDate = jest.spyOn(global, 'Date').mockImplementation(() => currentTime);
      service['cronStatus'] = { last_sync_time: lastSyncTime };
      
      const mockOldUser = {
        id: 'user3',
        tenantId: 'org1',
        program: [
          {
            programId: 'program1',
            role: { id: 'role1' }
          }
        ],
        // Ensure date is properly formatted as ISO string
        updated_at: '2025-06-30T10:00:00.000Z'
      };

      // Mock getRegisteredUsers to return user with old updated_at
      jest.spyOn(service, 'getRegisteredUsers').mockResolvedValue([mockOldUser]);
      jest.spyOn(service as any, 'saveCronStatus').mockImplementation(async () => {
        service['cronStatus'].last_sync_time = currentTime;
      });
      jest.spyOn(service, 'createEvent').mockResolvedValue(undefined);
      
      // Clear all mocks before test
      jest.clearAllMocks();
      
      // Reset console.log mock
      (console.log as jest.Mock).mockClear();

      await service.migrateUsers();

      expect(console.log).toHaveBeenNthCalledWith(1, 'user migration starting');
      expect(console.log).toHaveBeenNthCalledWith(2, 'User migration completed.');
      mockDate.mockRestore();
    });

    it('should handle no users to migrate', async () => {
      jest.spyOn(service, 'getRegisteredUsers').mockResolvedValue([]);

      await service.migrateUsers();

      expect(service.getRegisteredUsers).toHaveBeenCalledWith(service['cronStatus'].last_sync_time);
      expect(mockUserModel.findOne).not.toHaveBeenCalled();
      expect(mockUserModel.create).not.toHaveBeenCalled();
    });

    it('should generate events for updated users with multiple programs and roles', async () => {
      const currentTime = new Date('2025-06-30T11:49:41.000Z');
      const mockDate = jest.spyOn(global, 'Date').mockImplementation(() => currentTime);
      
      const mockUpdatedUser = {
        id: 'user4',
        organization_id: 'org1',
        program_ids: ['program1', 'program2'],
        role_ids: ['role1', 'role2'],
        updated_at: currentTime.toISOString()
      };

      jest.spyOn(service, 'getRegisteredUsers').mockResolvedValue([{
        id: 'user4',
        tenantId: 'org1',
        program: [
          { programId: 'program1', role: { id: 'role1' } },
          { programId: 'program2', role: { id: 'role2' } }
        ],
        updated_at: currentTime.toISOString()
      }]);

      mockUserModel.findOne.mockResolvedValue({
        program_ids: ['program1'],
        role_ids: ['role1']
      });

      jest.spyOn(service as any, 'saveCronStatus').mockImplementation(async () => {});
      jest.spyOn(service, 'createEvent').mockResolvedValue(undefined);

      await service.migrateUsers();

      expect(service.createEvent).toHaveBeenCalledWith([
        {
          organization_id: 'org1',
          entity_id: 'user4',
          program_id: 'program1',
          entity_role_id: 'role1',
          event_type: 'program_participation',
          program_participated_id: 'program1'
        },
        {
          organization_id: 'org1',
          entity_id: 'user4',
          program_id: 'program2',
          entity_role_id: 'role2',
          event_type: 'program_participation',
          program_participated_id: 'program2'
        }
      ]);
      mockDate.mockRestore();
    });

    it('should handle null role_ids in updated users', async () => {
      const currentTime = new Date('2025-06-30T11:49:41.000Z');
      const mockDate = jest.spyOn(global, 'Date').mockImplementation(() => currentTime);
      
      const mockUpdatedUser = {
        id: 'user5',
        organization_id: 'org1',
        program_ids: ['program1', 'program2'],
        role_ids: null,
        updated_at: currentTime.toISOString()
      };

      jest.spyOn(service, 'getRegisteredUsers').mockResolvedValue([{
        id: 'user5',
        tenantId: 'org1',
        program: [
          { programId: 'program1', role: null },
          { programId: 'program2', role: null }
        ],
        updated_at: currentTime.toISOString()
      }]);

      mockUserModel.findOne.mockResolvedValue({
        program_ids: ['program1'],
        role_ids: null
      });

      jest.spyOn(service as any, 'saveCronStatus').mockImplementation(async () => {});
      jest.spyOn(service, 'createEvent').mockResolvedValue(undefined);

      await service.migrateUsers();

      // No events should be created due to null role_ids
      expect(service.createEvent).not.toHaveBeenCalled();
      mockDate.mockRestore();
    });

    it('should handle users with empty program array', async () => {
      const currentTime = new Date('2025-06-30T11:49:41.000Z');
      const mockDate = jest.spyOn(global, 'Date').mockImplementation(() => currentTime);
      
      const mockUserWithEmptyProgram = {
        id: 'user6',
        tenantId: 'org1',
        program: [],
        updated_at: currentTime.toISOString()
      };

      jest.spyOn(service, 'getRegisteredUsers').mockResolvedValue([mockUserWithEmptyProgram]);
      mockUserModel.findOne.mockResolvedValue(null);

      jest.spyOn(service as any, 'saveCronStatus').mockImplementation(async () => {});
      jest.spyOn(service, 'createEvent').mockResolvedValue(undefined);

      await service.migrateUsers();

      // Verify only the registration event is created
      expect(service.createEvent).toHaveBeenCalledWith([{
        organization_id: 'org1',
        entity_id: 'user6',
        program_id: undefined,
        entity_role_id: undefined,
        event_type: 'date_of_registration',
        program_participated_id: undefined,
        registration_date: currentTime.toISOString()
      }]);
      mockDate.mockRestore();
    });

    it('should skip users not updated since last sync time', async () => {
      const currentTime = new Date('2025-06-30T11:49:41.000Z');
      const lastSyncTime = new Date('2025-06-30T11:00:00.000Z');
      const oldUpdateTime = new Date('2025-06-30T10:00:00.000Z');
      const mockDate = jest.spyOn(global, 'Date').mockImplementation(() => currentTime);
      service['cronStatus'] = { last_sync_time: lastSyncTime };
      
      const mockOldUser = {
        id: 'user7',
        tenantId: 'org1',
        program: [{
          programId: 'program1',
          role: { id: 'role1' }
        }],
        updated_at: oldUpdateTime.toISOString()
      };

      jest.spyOn(service, 'getRegisteredUsers').mockResolvedValue([mockOldUser]);
      mockUserModel.findOne.mockResolvedValue({
        program_ids: ['program1'],
        role_ids: ['role1']
      });

      jest.spyOn(service as any, 'saveCronStatus').mockImplementation(async () => {});
      jest.spyOn(service, 'createEvent').mockResolvedValue(undefined);

      await service.migrateUsers();

      // Verify no events are created since user is not updated
      expect(service.createEvent).toHaveBeenCalledWith([]);
      mockDate.mockRestore();
    });

    it('should handle users with null program array', async () => {
      const currentTime = new Date('2025-06-30T11:49:41.000Z');
      const mockDate = jest.spyOn(global, 'Date').mockImplementation(() => currentTime);
      
      const mockUserWithNullProgram = {
        id: 'user8',
        tenantId: 'org1',
        program: null,
        updated_at: currentTime.toISOString()
      };

      jest.spyOn(service, 'getRegisteredUsers').mockResolvedValue([mockUserWithNullProgram]);
      mockUserModel.findOne.mockResolvedValue(null);

      jest.spyOn(service as any, 'saveCronStatus').mockImplementation(async () => {});
      jest.spyOn(service, 'createEvent').mockResolvedValue(undefined);

      await service.migrateUsers();

      // Verify only the registration event is created
      expect(service.createEvent).toHaveBeenCalledWith([{
        organization_id: 'org1',
        entity_id: 'user8',
        program_id: undefined,
        entity_role_id: undefined,
        event_type: 'date_of_registration',
        program_participated_id: undefined,
        registration_date: currentTime.toISOString()
      }]);
      mockDate.mockRestore();
    });

    it('should handle errors during migration', async () => {
      jest.spyOn(service, 'getRegisteredUsers').mockRejectedValue(new Error('Something went wrong'));
      const consoleSpy = jest.spyOn(console, 'log');

      await service.migrateUsers();

      expect(consoleSpy).toHaveBeenCalledWith('user migration starting');
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });
});

