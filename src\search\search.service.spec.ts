import { Test, TestingModule } from '@nestjs/testing';
import { SearchService } from './search.service';
import { ErrorService } from '../error/error.service';
import { Connection } from 'mongoose';

describe('SearchService', () => {
  let service: SearchService;
  let connectionMock: jest.Mocked<Connection>;
  let errorServiceMock: { serverError: jest.Mock; notFoundError: jest.Mock };

  beforeEach(async () => {
    const mockCollection = {
      find: jest.fn(),
      toArray: jest.fn(),
    };

    connectionMock = {
      collection: jest.fn().mockReturnValue(mockCollection),
      db: {
        listCollections: jest.fn(),
      },
    } as unknown as jest.Mocked<Connection>;

    errorServiceMock = {
      serverError: jest.fn(),
      notFoundError: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SearchService,
        { provide: ErrorService, useValue: errorServiceMock },
        { provide: Connection, useValue: connectionMock }
      ],
    }).compile();

    service = module.get<SearchService>(SearchService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('search', () => {
    it('should return data when query succeeds', async () => {
      const mockResult = [{ id: 1 }];
      const mockQuery = { field: 'value' };
      const mockCollection = connectionMock.collection('users');
      const mockCursor = { toArray: jest.fn().mockResolvedValueOnce(mockResult) };
      mockCollection.find = jest.fn().mockReturnValue(mockCursor);

      const result = await service.search('users', JSON.stringify(mockQuery));
      
      expect(connectionMock.collection).toHaveBeenCalledWith('users');
      expect(result).toEqual({
        message: 'users gotten successfully',
        status: 'success',
        data: mockResult
      });
    });

    it('should retry with ObjectId when initial query returns empty', async () => {
      const mockId = '507f1f77bcf86cd799439011';
      const mockQuery = { _id: mockId };
      const mockCollection = connectionMock.collection('users');
      const emptyResult = [];
      const mockResult = [{ _id: mockId, name: 'Test' }];
      
      const mockCursor1 = { toArray: jest.fn().mockResolvedValueOnce(emptyResult) };
      const mockCursor2 = { toArray: jest.fn().mockResolvedValueOnce(mockResult) };
      
      mockCollection.find = jest.fn()
        .mockReturnValueOnce(mockCursor1)
        .mockReturnValueOnce(mockCursor2);

      const result = await service.search('users', JSON.stringify(mockQuery));
      
      expect(mockCollection.find).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        message: 'users gotten successfully',
        status: 'success',
        data: mockResult
      });
    });

    it('should return empty array when both normal and ObjectId queries fail', async () => {
      const mockId = '507f1f77bcf86cd799439011';
      const mockQuery = { _id: mockId };
      const mockCollection = connectionMock.collection('users');
      const emptyResult = [];
      
      const mockCursor1 = { toArray: jest.fn().mockResolvedValueOnce(emptyResult) };
      const mockCursor2 = { toArray: jest.fn().mockResolvedValueOnce(emptyResult) };
      
      mockCollection.find = jest.fn()
        .mockReturnValueOnce(mockCursor1)
        .mockReturnValueOnce(mockCursor2);

      const result = await service.search('users', JSON.stringify(mockQuery));
      
      expect(mockCollection.find).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        message: 'users gotten successfully',
        status: 'success',
        data: []
      });
    });

    it('should handle JSON parse error', async () => {
      const result = await service.search('users', 'invalid-json');
      
      expect(result).toEqual([]);
      expect(errorServiceMock.serverError).toHaveBeenCalledWith(
        expect.stringContaining('Error while searching:')
      );
    });

    it('should handle database query error', async () => {
      const mockQuery = { field: 'value' };
      const mockCollection = connectionMock.collection('users');
      const mockCursor = { toArray: jest.fn().mockRejectedValueOnce(new Error('DB failed')) };
      mockCollection.find = jest.fn().mockReturnValue(mockCursor);

      const result = await service.search('users', JSON.stringify(mockQuery));
      
      expect(result).toEqual([]);
      expect(errorServiceMock.serverError).toHaveBeenCalledWith(
        'Error while searching: DB failed'
      );
    });

    it('should handle nested ObjectId conversion', async () => {
      const mockId1 = '507f1f77bcf86cd799439011';
      const mockId2 = '507f1f77bcf86cd799439012';
      const mockQuery = { 
        $and: [
          { _id: mockId1 },
          { parent_id: mockId2 },
          { nested: { id: mockId1 } },
          { nullValue: null },
          { invalidObjectId: 'not-an-object-id' }
        ]
      };
      const mockCollection = connectionMock.collection('users');
      const emptyResult = [];
      const mockResult = [{ _id: mockId1, parent_id: mockId2, name: 'Test' }];
      
      const mockCursor1 = { toArray: jest.fn().mockResolvedValueOnce(emptyResult) };
      const mockCursor2 = { toArray: jest.fn().mockResolvedValueOnce(mockResult) };
      
      mockCollection.find = jest.fn()
        .mockReturnValueOnce(mockCursor1)
        .mockReturnValueOnce(mockCursor2);

      const result = await service.search('users', JSON.stringify(mockQuery));
      
      expect(mockCollection.find).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        message: 'users gotten successfully',
        status: 'success',
        data: mockResult
      });
    });
  });
});
