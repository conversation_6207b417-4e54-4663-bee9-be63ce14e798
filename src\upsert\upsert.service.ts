import { Injectable } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection, Types } from 'mongoose';
import { ErrorService } from '../error/error.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class UpsertService {
  constructor(
    private readonly userService: UsersService,
    private readonly errorService: ErrorService,
    @InjectConnection() private readonly connection: Connection,
  ) {}

  private tryConvertToObjectId(query: any): any {
    if (typeof query !== 'object' || query === null) return query;

    const result: any = Array.isArray(query) ? [] : {};

    for (const key in query) {
      const value = query[key];

      if (value && typeof value === 'object') {
        if ('$oid' in value) {
          // Handle ObjectId in extended JSON format
          try {
            result[key] = new Types.ObjectId(value.$oid);
          } catch {
            result[key] = value;
          }
        } else if ('$date' in value) {
          // Handle Date in extended JSON format
          try {
            result[key] = new Date(value.$date);
          } catch {
            result[key] = value;
          }
        } else if (Array.isArray(value)) {
          // Handle arrays (like roles and badges)
          result[key] = value.map((item) => this.tryConvertToObjectId(item));
        } else {
          // Recursively handle nested objects
          result[key] = this.tryConvertToObjectId(value);
        }
      } else if (typeof value === 'string' && /^[0-9a-fA-F]{24}$/.test(value)) {
        // Handle plain string ObjectIds
        try {
          result[key] = new Types.ObjectId(value);
        } catch {
          result[key] = value;
        }
      } else {
        result[key] = value;
      }
    }

    return result;
  }

  public async upsert(
    resource: string,
    operation: 'insert' | 'update',
    data: any[],
    where?: any[],
  ): Promise<any> {
    try {
      const collection = this.connection.collection(resource);

      // Convert potential ObjectId strings in data and where clause
      console.log("data is",data);
      const convertedData = this.tryConvertToObjectId(data);
      const convertedWhere = where ? this.tryConvertToObjectId(where) : null;
      console.log("converted data is",convertedData);

      if (!Array.isArray(data)) {
        this.errorService.serverError('Data must be an array');
        return undefined;
      }

      let bulkOps;
      if (operation === 'insert') {
        const updated: any[] = [];
        const inserted: any[] = [];

        if (resource === 'users') {
          for (const user of convertedData) {
            const existingUser: any = await collection.findOne({ id: user.id });
            if (existingUser) {
              if ((existingUser.program_ids?.length || 0) !== (user.program_ids?.length || 0)) {
                updated.push(user);
              }
            } else {
              inserted.push(user);
            }
          }

          // Handle program participation events for updated users
          const participationEvents = updated.flatMap((user) => {
            const events: any[] = [];
            for (let i = 0; i < (user.program_ids?.length || 0); i++) {
              events.push({
                organization_id: user.organization_id,
                entity_id: user.id,
                program_id: user.program_ids[i],
                entity_role_id: user.role_ids[i],
                event_type: 'program_participation',
                program_participated_id: user.program_ids[i],
              });
            }
            return events;
          });

          const newUserEvents = inserted.flatMap((user) => {
            const events: any[] = [];

            events.push({
              organization_id: user.organization_id,
              entity_id: user.id,
              program_id: user?.program_ids?.[0],
              entity_role_id: user?.role_ids?.[0],
              event_type: 'date_of_registration',
              program_participated_id: user?.program_ids?.[0],
              registration_date: new Date().toISOString(),
            });

            for (let i = 0; i < (user.program_ids?.length || 0); i++) {
              events.push({
                organization_id: user.organization_id,
                entity_id: user.id,
                program_id: user.program_ids[i],
                entity_role_id: user.role_ids[i],
                event_type: 'program_participation',
                program_participated_id: user.program_ids[i],
              });
            }

            return events;
          });

          if (participationEvents.length > 0) {
            this.userService.createEvent(participationEvents);
          }
          if (newUserEvents.length > 0) {
            this.userService.createEvent(newUserEvents);
          }
        }

        bulkOps = convertedData.map((item) => {
          const { id, ...updateData } = item;

          return {
            updateOne: {
              filter: { id },
              update: { $set: updateData },
              upsert: true,
            },
          };
        });
      } else if (operation === 'update') {
        if (!Array.isArray(where) || where.length !== data.length) {
          this.errorService.serverError('Both data and where must be arrays of the same length for update operation');
          return undefined;
        }
        const updated: any[] = [];
        const inserted: any[] = [];

        if (resource == 'users') {
          where.forEach(async (whereUser, index) => {
            const existingUser: any = await collection.findOne({
              id: whereUser.id,
            });
            if (existingUser) {
              const newUser = data[index];
              if (
                (existingUser.program_ids?.length || 0) !== (newUser.program_ids?.length || 0)
              ) {
                updated.push(newUser);
              }
            }
          });
          const mappedUsers: any[] = updated.flatMap((user) => {
            const events: any[] = [];
            for (let i = 0; i < (user.program_ids?.length || 0); i++) {
              events.push({
                organization_id: user.organization_id,
                entity_id: user.id,
                program_id: user.program_ids[i],
                entity_role_id: user.role_ids[i],
                event_type: 'program_participation',
                program_participated_id: user.program_ids[i],
              });
            }
            return events;
          });
          this.userService.createEvent(mappedUsers);
        }

        bulkOps = convertedData.map((item, index) => {
          const { id, ...updateData } = item;

          return {
            updateOne: {
              filter: convertedWhere[index],
              update: { $set: updateData },
              upsert: true,
            },
          };
        });
      }

      const result = await collection.bulkWrite(bulkOps);
      return {
        status: 'success',
        message: `${resource} ${operation} successfully`,
        ok: result.ok === 1,
        modifiedCount: result.modifiedCount,
        upsertedCount: result.upsertedCount,
        upsertedIds: result.upsertedIds,
        matchedCount: result.matchedCount,
      };
    } catch (err) {
      console.log("Error upserting is",err);
      this.errorService.serverError(err.message);
      return undefined;
    }
  }
}
