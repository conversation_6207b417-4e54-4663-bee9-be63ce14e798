import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import * as basicAuth from 'express-basic-auth';
import { ConfigService } from '@nestjs/config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const config = new DocumentBuilder()
    .setTitle('Activity Service API Docs')
    // .setExternalDoc('Postman Collection')
    .setDescription('This is the Activity Service API documentation')
    .setVersion('1.0')
    .addTag('Activity Service')
    .build();

  if (!process.env.SWAGGER_PASSWORD) {
    throw new Error('SWAGGER_PASSWORD environment variable is required');
  }
  const document = SwaggerModule.createDocument(app, config);
  app.use(
    '/api/docs',
    basicAuth({
      challenge: true,
      users: { admin: process.env.SWAGGER_PASSWORD },
    }),
  );
  SwaggerModule.setup('api/docs', app, document);

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  const port = app.get(ConfigService).get<number>('PORT') || 3000;
  await app.listen(port, () => {
    console.log(`Server is Listening on port ${port}`);
  });
}
bootstrap();
