import { Test, TestingModule } from '@nestjs/testing';
import { EntityPortfolioService } from './entity-portfolio.service';
import { ErrorService } from '../error/error.service';
import { getModelToken } from '@nestjs/mongoose';
import { EntityAssignment } from '../entity-assignment/schema/entity-assignment.schema';
import { EntityPortfolio } from './schema/entity-portfolo.schema';
import { EntityType } from '../entity-assignment/enum/entity-assignment.enum';
import { Types } from 'mongoose';
import axios from 'axios';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('EntityPortfolioService', () => {
  let service: EntityPortfolioService;
  let mockErrorService: any;
  let mockEntityAssignmentModel: any;
  let mockEntityPortfolioModel: any;

  beforeEach(async () => {
    mockErrorService = {
      serverError: jest.fn().mockImplementation((msg) => {
        throw new Error(msg);
      }),
    };

    mockEntityAssignmentModel = {
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
    };

    mockEntityPortfolioModel = {
      find: jest.fn(),
      findOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntityPortfolioService,
        {
          provide: ErrorService,
          useValue: mockErrorService,
        },
        {
          provide: getModelToken(EntityAssignment.name),
          useValue: mockEntityAssignmentModel,
        },
        {
          provide: getModelToken(EntityPortfolio.name),
          useValue: mockEntityPortfolioModel,
        },
      ],
    }).compile();

    service = module.get<EntityPortfolioService>(EntityPortfolioService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('buildPortfolio', () => {
    const mockUserId = 'user-1';
    const mockRoleId = '507f1f77bcf86cd799439011';
    const mockOrgId = 'org-1';
    const mockProgramId = '507f1f77bcf86cd799439012';

    it('should return empty array if user is already processed', async () => {
      const processedSet = new Set([mockUserId]);
      
      const result = await service.buildPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId,
        processedSet
      );

      expect(result).toEqual([]);
      expect(mockEntityAssignmentModel.find).not.toHaveBeenCalled();
    });

    it('should build portfolio with assets and subordinates', async () => {
      const mockAssets = [
        { assigned_id: 'asset-1' },
        { assigned_id: 'asset-2' },
      ];

      const mockSubordinates = [
        { 
          assigned_id: 'sub-1', 
          assigned_role_id: new Types.ObjectId('507f1f77bcf86cd799439011'),
          assigned_to_role_id: new Types.ObjectId('507f1f77bcf86cd799439011')
        },
      ];

      mockEntityAssignmentModel.find.mockImplementation((query) => {
        if (query.assigned_to_entity_type === EntityType.ASSET) {
          return Promise.resolve(mockAssets);
        }
        if (query.assigned_role_id && query.assigned_role_id.toString() === '507f1f77bcf86cd799439011') {
          return Promise.resolve(mockSubordinates);
        }
        return Promise.resolve([]);
      });

      process.env.CONFIG_SERVICE_BASE_URL = 'http://localhost:3000';
      mockedAxios.get.mockImplementation((url) => {
        if (url.includes('/role/child_portfolios/')) {
          return Promise.resolve({ data: ['507f1f77bcf86cd799439011'] });
        }
        return Promise.reject(new Error('Invalid URL'));
      });

      const expectedPortfolio = [
        { id: mockUserId, role_id: mockRoleId },
        { id: 'asset-1', role_id: mockRoleId },
        { id: 'asset-2', role_id: mockRoleId },
        { id: 'sub-1', role_id: mockRoleId },
        { id: 'asset-1', role_id: mockRoleId },
        { id: 'asset-2', role_id: mockRoleId }
      ];

      mockEntityPortfolioModel.findOneAndUpdate.mockResolvedValue({
        id: mockUserId,
        role_id: mockRoleId,
        portfolio: expectedPortfolio,
        organization_id: mockOrgId,
        program_id: mockProgramId,
        is_valid: 1,
        updated_at: new Date()
      });

      const updatedPortfolio = await service.buildPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      );

      expect(updatedPortfolio).toEqual(expectedPortfolio);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'http://localhost:3000/role/child_portfolios/507f1f77bcf86cd799439011'
      );
      expect(mockEntityPortfolioModel.findOneAndUpdate).toHaveBeenCalledWith(
        { id: mockUserId, role_id: new Types.ObjectId(mockRoleId) },
        {
          id: mockUserId,
          role_id: new Types.ObjectId(mockRoleId),
          is_valid: 1,
          portfolio: expectedPortfolio,
          updated_at: expect.any(Date),
          organization_id: mockOrgId,
          program_id: mockProgramId,
        },
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true,
        }
      );
    });

    it('should handle multiple levels of subordinates', async () => {
      const mockAssets = [{ assigned_id: 'asset-1' }];
      const mockSubLevel1 = [{ 
        assigned_id: 'sub-1',
        assigned_role_id: new Types.ObjectId('507f1f77bcf86cd799439012'),
        assigned_to_role_id: new Types.ObjectId(mockRoleId)
      }];

      mockEntityAssignmentModel.find.mockImplementation((query) => {
        if (query.assigned_to_entity_type === EntityType.ASSET) {
          return Promise.resolve(mockAssets);
        }
        if (query.assigned_to_id === mockUserId) {
          return Promise.resolve(mockSubLevel1);
        }
        return Promise.resolve([]);
      });

      mockedAxios.get.mockImplementation((url) => {
        return Promise.resolve({ data: ['507f1f77bcf86cd799439012'] });
      });

      const expectedPortfolio = [
        { id: mockUserId, role_id: mockRoleId },
        { id: 'asset-1', role_id: mockRoleId },
        { id: 'sub-1', role_id: '507f1f77bcf86cd799439012' },
        { id: 'asset-1', role_id: '507f1f77bcf86cd799439012' }
      ];

      mockEntityPortfolioModel.findOneAndUpdate.mockResolvedValue({
        id: mockUserId,
        role_id: mockRoleId,
        portfolio: expectedPortfolio,
        organization_id: mockOrgId,
        program_id: mockProgramId,
        is_valid: 1,
        updated_at: new Date()
      });

      const updatedPortfolio = await service.buildPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      );

      expect(updatedPortfolio).toEqual(expectedPortfolio);
    });

    it('should handle getChildPortfolio axios error', async () => {
      mockEntityAssignmentModel.find.mockResolvedValue([]);
      mockedAxios.get.mockRejectedValue(new Error('Config service error'));

      await expect(service.buildPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      )).rejects.toThrow('Config service error');

      expect(mockErrorService.serverError).toHaveBeenCalledWith('Config service error');
    });

    it('should handle database errors', async () => {
      mockEntityAssignmentModel.find.mockRejectedValueOnce(new Error('DB error'));

      await expect(service.buildPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      )).rejects.toThrow('DB error');

      expect(mockErrorService.serverError).toHaveBeenCalledWith('DB error');
    });
  });

  describe('getStaffPortfolio', () => {
    const mockUserId = 'user-1';
    const mockRoleId = '507f1f77bcf86cd799439011';
    const mockOrgId = 'org-1';
    const mockProgramId = '507f1f77bcf86cd799439012';

    it('should return error for invalid parameters', async () => {
      const testCases = [
        { user_id: '', role_id: mockRoleId, org_id: mockOrgId, program_id: mockProgramId },
        { user_id: mockUserId, role_id: '', org_id: mockOrgId, program_id: mockProgramId },
        { user_id: mockUserId, role_id: mockRoleId, org_id: '', program_id: mockProgramId },
        { user_id: mockUserId, role_id: mockRoleId, org_id: mockOrgId, program_id: '' }
      ];

      for (const testCase of testCases) {
        const result = await service.getStaffPortfolio(
          testCase.user_id,
          testCase.role_id,
          testCase.org_id,
          testCase.program_id
        );

        expect(result).toEqual({
          message: 'Invalid parameters',
          status: 'error',
        });
      }
    });

    it('should return existing portfolio if found with last_sync_time', async () => {
      const mockLastSyncTime = '2025-06-27T09:19:23.000Z';
      const mockPortfolio = [{
        id: mockUserId,
        portfolio: [{ id: 'asset-1' }],
        updated_at: new Date('2025-06-27T10:19:23.000Z')
      }];

      mockEntityPortfolioModel.find.mockResolvedValue(mockPortfolio);

      const result = await service.getStaffPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId,
        mockLastSyncTime
      );

      expect(result).toEqual({
        message: 'Entity portfolio gotten successfully',
        status: 'success',
        data: mockPortfolio,
      });

      expect(mockEntityPortfolioModel.find).toHaveBeenCalledWith({
        id: mockUserId,
        role_id: mockRoleId,
        is_valid: 1,
        organization_id: mockOrgId,
        program_id: mockProgramId,
        updated_at: { $gte: new Date(mockLastSyncTime) }
      });
    });

    it('should build new portfolio if not found', async () => {
      mockEntityPortfolioModel.find.mockResolvedValueOnce([]);
      
      const mockNewPortfolio = [{
        id: mockUserId,
        role_id: new Types.ObjectId(mockRoleId),
        portfolio: [{ id: 'new-asset-1' }],
      }];

      jest.spyOn(service, 'buildPortfolio').mockResolvedValueOnce([{ id: 'new-asset-1' }]);
      mockEntityPortfolioModel.find.mockResolvedValueOnce(mockNewPortfolio);

      const result = await service.getStaffPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      );

      expect(result).toEqual({
        message: 'Entity portfolio gotten successfully',
        status: 'success',
        data: mockNewPortfolio,
      });

      expect(service.buildPortfolio).toHaveBeenCalledWith(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      );
    });

    it('should handle invalid ObjectId conversion', async () => {
      const invalidRoleId = 'invalid-id';
      mockEntityPortfolioModel.find.mockImplementation(() => {
        throw new Error('Invalid ObjectId');
      });

      await expect(service.getStaffPortfolio(
        mockUserId,
        invalidRoleId,
        mockOrgId,
        mockProgramId
      )).rejects.toThrow('Invalid ObjectId');

      expect(mockErrorService.serverError).toHaveBeenCalledWith('Invalid ObjectId');
    });

    it('should handle database errors', async () => {
      mockEntityPortfolioModel.find.mockRejectedValueOnce(new Error('DB error'));

      await expect(service.getStaffPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      )).rejects.toThrow('DB error');

      expect(mockErrorService.serverError).toHaveBeenCalledWith('DB error');
    });
  });

  describe('getStaffPortfolioByAssignment', () => {
    const mockUserId = 'user-1';
    const mockRoleId = '507f1f77bcf86cd799439011';
    const mockOrgId = 'org-1';
    const mockProgramId = '507f1f77bcf86cd799439012';

    it('should get portfolio by assignment with last_sync_time', async () => {
      const mockLastSyncTime = '2025-06-27T09:19:23.000Z';
      const mockPortfolioData = {
        data: [{
          portfolio: [
            { id: 'portfolio-1' },
            { id: 'portfolio-2' },
          ],
        }],
      };

      const mockPortfolios = [
        { id: 'portfolio-1', data: 'data1' },
        { id: 'portfolio-2', data: 'data2' },
      ];

      jest.spyOn(service, 'getStaffPortfolio').mockResolvedValueOnce(mockPortfolioData);
      mockEntityPortfolioModel.find.mockResolvedValueOnce(mockPortfolios);

      const result = await service.getStaffPortfolioByAssignment(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId,
        mockLastSyncTime
      );

      expect(result).toEqual({
        message: 'Entity portfolio gotten successfully',
        status: 'success',
        data: mockPortfolios,
      });

      expect(service.getStaffPortfolio).toHaveBeenCalledWith(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId,
        mockLastSyncTime
      );
    });

    it('should handle empty portfolio data', async () => {
      const mockPortfolioData = {
        data: [{ portfolio: [] }]
      };

      jest.spyOn(service, 'getStaffPortfolio').mockResolvedValueOnce(mockPortfolioData);
      mockEntityPortfolioModel.find.mockResolvedValueOnce([]);

      const result = await service.getStaffPortfolioByAssignment(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      );

      expect(result).toEqual({
        message: 'Entity portfolio gotten successfully',
        status: 'success',
        data: [],
      });
    });

    it('should handle null portfolio data', async () => {
      const mockPortfolioData = {
        data: null
      };

      jest.spyOn(service, 'getStaffPortfolio').mockResolvedValueOnce(mockPortfolioData);

      const result = await service.getStaffPortfolioByAssignment(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      );

      expect(result).toEqual({
        message: 'Entity portfolio gotten successfully',
        status: 'success',
        data: undefined,
      });
    });

    it('should handle invalid program_id format', async () => {
      const invalidProgramId = 'invalid-id';
      mockErrorService.serverError.mockImplementation(() => {
        throw new Error('Invalid ObjectId');
      });

      await expect(service.getStaffPortfolioByAssignment(
        mockUserId,
        mockRoleId,
        mockOrgId,
        invalidProgramId
      )).rejects.toThrow('Invalid ObjectId');
    });

    it('should handle database errors', async () => {
      mockEntityPortfolioModel.find.mockRejectedValueOnce(new Error('DB error'));

      await expect(service.getStaffPortfolioByAssignment(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      )).rejects.toThrow('DB error');

      expect(mockErrorService.serverError).toHaveBeenCalledWith('DB error');
    });
  });
});
