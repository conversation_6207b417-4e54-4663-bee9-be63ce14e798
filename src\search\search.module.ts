import { Modu<PERSON> } from '@nestjs/common';
import { SearchService } from './search.service';
import { SearchController } from './search.controller';
import { ErrorModule } from '../error/error.module';
import { MongooseModule, getConnectionToken } from '@nestjs/mongoose';
import { Connection } from 'mongoose';

@Module({
  imports: [
    ErrorModule,
    MongooseModule.forFeature([])
  ],
  controllers: [SearchController],
  providers: [
    SearchService,
    {
      provide: Connection,
      useFactory: (connection) => connection,
      inject: [getConnectionToken()]
    }
  ],
})
export class SearchModule {}
