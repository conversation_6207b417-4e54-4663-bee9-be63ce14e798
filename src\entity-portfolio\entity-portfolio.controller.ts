import { Controller, Get, Headers, Query } from '@nestjs/common';
import { EntityPortfolioService } from './entity-portfolio.service';
import { ApiOperation, ApiHeader, ApiParam, ApiQuery } from '@nestjs/swagger';

@Controller('entity-portfolio')
export class EntityPortfolioController {
  constructor(
    private readonly entityPortfolioService: EntityPortfolioService,
  ) {}

  @Get('by-assignment')
  @ApiOperation({
    summary: 'Get entity portfolio',
  })
  @ApiHeader({
    name: 'user-id',
    description: 'ID of the user',
    required: true,
  })
  @ApiParam({
    name: 'role_id',
    description: 'ID of the role',
    required: true,
  })
  @ApiParam({
    name: 'program_id',
    description: 'ID of the program',
    required: true,
  })
  @ApiQuery({
    name: 'last_sync_time',
    description: 'Last sync time',
    required: false,
  })
  async getEntityPortfolio(
    @Headers('user-id') user_id: string,
    @Query('role_id') role_id: string,
    @Headers('tenant-id') organization_id: string,
    @Query('program_id') program_id: string,
    @Query('last_sync_time') last_sync_time?: string,
  ): Promise<any> {
    return this.entityPortfolioService.getStaffPortfolioByAssignment(
      user_id,
      role_id,
      organization_id,
      program_id,
      last_sync_time,
    );
  }
  @Get()
  @ApiOperation({
    summary: 'Get assigned users',
  })
  @ApiHeader({
    name: 'user-id',
    description: 'ID of the user',
    required: true,
  })
  @ApiHeader({
    name: 'tenant-id',
    description: 'ID of the tenant',
    required: true,
  })
  @ApiParam({
    name: 'role_id',
    description: 'ID of the role',
    required: true,
  })
  @ApiParam({
    name: 'program_id',
    description: 'ID of the program',
    required: true,
  })
  @ApiQuery({
    name: 'last_sync_time',
    description: 'Last sync time',
    required: false,
  })
  async getEntityAssignment(
    @Query('user_id') user_id: string,
    @Query('tenant_id') tenant_id: string,
    @Query('role_id') role_id: string,
    @Query('program_id') program_id: string,
    @Query('last_sync_time') last_sync_time?: string,
  ): Promise<any> {
    return this.entityPortfolioService.getStaffPortfolio(
      user_id,
      role_id,
      tenant_id,
      program_id,
      last_sync_time,
    );
  }
}
