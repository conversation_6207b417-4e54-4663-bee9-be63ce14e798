import { Test, TestingModule } from '@nestjs/testing';
import { AssetController } from './asset.controller';
import { payload } from './mocks/mock-values.mock';
import { AssetService } from './asset.service';

describe('AssetController', () => {
  let controller: AssetController;
  let service: AssetService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AssetController],
      providers: [{
        provide: AssetService,
        useValue: {
          createAsset: jest.fn().mockResolvedValue([payload]),
          getAssetById: jest.fn().mockResolvedValue(payload),
          getAllAssets: jest.fn().mockResolvedValue([payload]),
          getAssetAssignment: jest.fn().mockResolvedValue([payload])
        },
      }],
    }).compile();

    controller = module.get<AssetController>(AssetController);
    service = module.get<AssetService>(AssetService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createAsset', () => {
    it('should create an asset', async () => {
      const result = await controller.createAsset([payload]);
      
      expect(result).toBeDefined();
      expect(service.createAsset).toHaveBeenCalledWith([payload]);
      expect(result).toEqual([payload]);
    });

    it('should handle errors when creating an asset', async () => {
      jest.spyOn(service, 'createAsset').mockRejectedValue(new Error('Failed to create asset'));

      await expect(controller.createAsset([payload])).rejects.toThrow('Failed to create asset');
    });
  });
  describe('getAssetById', () => {
    it('should get an asset by id', async () => {
      const assetId = 'test-id';
      const result = await controller.getAssetById(assetId);
      
      expect(service.getAssetById).toHaveBeenCalledWith(assetId);
      expect(result).toEqual(payload);
    });

    it('should handle errors when getting an asset by id', async () => {
      const assetId = 'test-id';
      jest.spyOn(service, 'getAssetById').mockRejectedValue(new Error('Failed to get asset'));

      await expect(controller.getAssetById(assetId)).rejects.toThrow('Failed to get asset');
    });
  });

  describe('getAllAssets', () => {
    it('should get all assets for an organization', async () => {
      const orgId = 'org-id';
      const result = await controller.getAllAssets(orgId);
      
      expect(service.getAllAssets).toHaveBeenCalledWith(orgId, undefined);
      expect(result).toEqual([payload]);
    });

    it('should get all assets with last_sync_time', async () => {
      const orgId = 'org-id';
      const lastSyncTime = '2025-06-27T09:19:23.000Z';
      const result = await controller.getAllAssets(orgId, lastSyncTime);
      
      expect(service.getAllAssets).toHaveBeenCalledWith(orgId, lastSyncTime);
      expect(result).toEqual([payload]);
    });

    it('should handle errors when getting assets', async () => {
      const orgId = 'org-id';
      jest.spyOn(service, 'getAllAssets').mockRejectedValue(new Error('Failed to get assets'));

      await expect(controller.getAllAssets(orgId)).rejects.toThrow('Failed to get assets');
    });
  });

  describe('getAssignedUser', () => {
    it('should get assigned assets for a user', async () => {
      const userId = 'user-1';
      const tenantId = 'tenant-1';
      const roleId = 'role-1';
      const programId = 'program-1';
      const lastSyncTime = '2025-06-27T09:19:23.000Z';

      const result = await controller.getAssignedUser(
        userId,
        tenantId,
        roleId,
        programId,
        lastSyncTime
      );

      expect(service.getAssetAssignment).toHaveBeenCalledWith(
        userId,
        tenantId,
        roleId,
        programId,
        lastSyncTime
      );
      expect(result).toEqual([payload]);
    });

    it('should get assigned assets without last sync time', async () => {
      const userId = 'user-1';
      const tenantId = 'tenant-1';
      const roleId = 'role-1';
      const programId = 'program-1';

      const result = await controller.getAssignedUser(
        userId,
        tenantId,
        roleId,
        programId
      );

      expect(service.getAssetAssignment).toHaveBeenCalledWith(
        userId,
        tenantId,
        roleId,
        programId,
        undefined
      );
      expect(result).toEqual([payload]);
    });

    it('should handle errors when getting assigned assets', async () => {
      const userId = 'user-1';
      const tenantId = 'tenant-1';
      const roleId = 'role-1';
      const programId = 'program-1';

      jest.spyOn(service, 'getAssetAssignment').mockRejectedValue(new Error('Failed to get assigned assets'));

      await expect(controller.getAssignedUser(
        userId,
        tenantId,
        roleId,
        programId
      )).rejects.toThrow('Failed to get assigned assets');
    });
  });
});
