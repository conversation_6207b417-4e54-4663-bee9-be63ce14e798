import { Types } from 'mongoose';
import { CreateAssetDto } from '../dto/asset.dto';

export const payload: CreateAssetDto = 
  {
    organization_id: new Types.ObjectId('507f1f77bcf86cd799439011'),
    role: new Types.ObjectId('507f1f77bcf86cd799439012'),
    badge_ids: [new Types.ObjectId('507f1f77bcf86cd799439013')],
    response: [
      {
        id: 'screen-1',
        sections: [
          {
            id: 'section-1',
            components: [
              { id: 'component-1', label: 'name', answer: 'Entity1' },
              { id: 'component-2', label: 'value', answer: 'Value1' },
            ],
          },
        ],
      },
    ],
    entity: [new Types.ObjectId('507f1f77bcf86cd799439013')],
    program_ids: [new Types.ObjectId('507f1f77bcf86cd799439013')],
    unique_entity_id: '507f1f77bcf86cd799439013',
    response_id: '507f1f77bcf86cd799439013',
    target_id: '507f1f77bcf86cd799439013',
    performer_id: '507f1f77bcf86cd799439013',
  };
