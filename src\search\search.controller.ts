import { <PERSON>, Post, UseGuards, Query, Get } from '@nestjs/common';
import { SearchService } from './search.service';
import { QueryParams } from './interface/search.interface';
import { ErrorService } from '../error/error.service';

@Controller('search')
export class SearchController {
  constructor(
    private readonly searchService: SearchService,
    private readonly errorService: ErrorService
  ) {}

  @Get('/:table_name')
  async executeQuery(@Query() queryParams: QueryParams) {
    // console.log(queryParams)
    try {
      const query = decodeURIComponent(queryParams.query);
      JSON.parse(query); // Validate JSON
      const result = await this.searchService.search(queryParams.resource, query);
      return result;
    } catch (error) {
      if (error instanceof SyntaxError) {
        this.errorService.badRequestError('Invalid JSON in query parameter');
        return [];
      }
      throw error;
    }
  }
}
