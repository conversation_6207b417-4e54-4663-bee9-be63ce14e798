name: Build and Deploy to Google Kubernetes Engine

on:
  push:
    branches:
      - production

env:
  WIF_PROVIDER: ${{ secrets.WIF_PROVIDER }}
  SA_EMAIL: ${{ secrets.SA_EMAIL }}
  GKE_PROJECT: ${{ secrets.GKE_PROJECT }}
  GKE_CLUSTER: ${{ vars.GKE_CLUSTER }}
  REGION: ${{ vars.REGION }}
  DEPLOYMENT_NAME: ${{ vars.SERVICE_NAME }}
  REPOSITORY: ${{ vars.ARTIFACT_REPO }}
  GITHUB_SHA: ${{ github.sha }}
  main: .

jobs:
  build-and-push:
    name: Build and Push Image to Artifact Repo
    runs-on: ubuntu-latest
    continue-on-error: false
    environment: production
    permissions:
      contents: 'read'
      id-token: 'write'
    outputs:
      short_sha: ${{ steps.truncate_sha.outputs.short_sha }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        id: gcp-auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ env.WIF_PROVIDER }}
          service_account: ${{ env.SA_EMAIL }}

      - name: Configure Docker to use gcloud as a credential helper
        run: |
          gcloud auth configure-docker $REGION-docker.pkg.dev --quiet

      - name: Truncate SHA
        id: truncate_sha
        run: echo "short_sha=${GITHUB_SHA:0:7}" >> $GITHUB_OUTPUT

      - name: Build Docker Image
        run: |
          docker build -t ${{ env.REGION }}-docker.pkg.dev/${{ env.GKE_PROJECT }}/${{ env.REPOSITORY }}/${{ env.DEPLOYMENT_NAME }}:${{ steps.truncate_sha.outputs.short_sha }} .
        
      - name: Publish
        run: |
          docker push ${{ env.REGION }}-docker.pkg.dev/${{ env.GKE_PROJECT }}/${{ env.REPOSITORY }}/${{ env.DEPLOYMENT_NAME }}:${{ steps.truncate_sha.outputs.short_sha }}

      - name: Slack Notification
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: devops-app-notification
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_LINK_NAMES: true
          SLACK_MSG_AUTHOR: ${{ github.actor }}
          SLACK_TITLE: AgricOS-v25 Entity Service
          SLACK_MESSAGE: "Image Build failed for Commit - ${{ github.event.head_commit.message }}"
          SLACK_USERNAME: "rtCamp"
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

  dev-deploy:
    name: Deploy Image to Kubernetes Engine
    runs-on: ubuntu-latest
    needs: [build-and-push]
    environment: production
    permissions:
      contents: 'read'
      id-token: 'write'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Authenticate to Google Cloud
        id: gcp-auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ env.WIF_PROVIDER }}
          service_account: ${{ env.SA_EMAIL }}

      # Set up kustomize
      - name: Set up Kustomize
        run: |
          curl -o kustomize --location https://github.com/kubernetes-sigs/kustomize/releases/download/v3.1.0/kustomize_3.1.0_linux_amd64
          chmod u+x ./kustomize
        working-directory: ${{ env.main }}

      ## Get credentials
      - id: 'get-credentials'
        uses: 'google-github-actions/get-gke-credentials@v1'
        with:
          cluster_name:  ${{ env.GKE_CLUSTER }}
          location: ${{ env.REGION }}

      - name: Deploy to Kubernetes Engine
        run: |
          sed -i -e 's|REGION|'"$REGION"'|g' ./prod-manifests/deployment.yaml
          sed -i -e 's|GKE_PROJECT|'"$GKE_PROJECT"'|g' ./prod-manifests/deployment.yaml
          sed -i -e 's|REPOSITORY|'"$REPOSITORY"'|g' ./prod-manifests/deployment.yaml
          sed -i -e 's|DEPLOYMENT_NAME|'"$DEPLOYMENT_NAME"'|g' ./prod-manifests/deployment.yaml
          sed -i -e 's|SHORT_SHA|'"${{ needs.build-and-push.outputs.short_sha }}"'|g' ./prod-manifests/deployment.yaml
          ./kustomize build ./prod-manifests | kubectl apply -f -
          kubectl rollout status deployment/$DEPLOYMENT_NAME
          kubectl get services -o wide
        working-directory: ${{ env.main }}

      - name: Slack Notification
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: devops-app-notification
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_LINK_NAMES: true
          SLACK_MSG_AUTHOR: ${{ github.actor }}
          SLACK_TITLE: Agricos25 Entity Service (Production)
          SLACK_MESSAGE: "Kubernetes Deployment ${{ job.status }} for Commit - ${{ github.event.head_commit.message }}"
          SLACK_USERNAME: "rtCamp"
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}