import { Modu<PERSON> } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from './schema/user.schema';
import { ErrorService } from '../error/error.service';
import { UtilsModule } from '../utils/utils.module';
import { EntityPortfolioModule } from '../entity-portfolio/entity-portfolio.module';
import { AssetModule } from '../asset/asset.module';
@Module({
  imports: [
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
    UtilsModule,
    EntityPortfolioModule,
    AssetModule,
  ],
  controllers: [UsersController],
  providers: [UsersService, ErrorService],
  exports: [UsersService],
})
export class UsersModule {}
