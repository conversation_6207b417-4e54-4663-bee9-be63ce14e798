import { Types } from "mongoose";
import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON><PERSON>, IsMongoId, IsString } from "class-validator";

export class BadgeDto {
    @IsString()
    @ApiProperty({
        description: 'User ID',
        example: '507f1f77bcf86cd799439011',
      })
    id: string;
    @IsArray()
    @ApiProperty({
        description: 'Badge ID',
        example: ['507f1f77bcf86cd799439011'],
      })
    badge_id: Array<Types.ObjectId>;
    @IsMongoId()
    @ApiProperty({
        description: 'Organization ID',
        example: '507f1f77bcf86cd799439011',
      })
    organization_id: Types.ObjectId;
}