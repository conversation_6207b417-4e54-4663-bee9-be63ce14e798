import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AssetController } from './asset.controller';
import { AssetService } from './asset.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Asset, AssetSchema } from './schema/asset.schema';
import { ErrorService } from '../error/error.service';
import { UtilsModule } from '../utils/utils.module';
import { EntityPortfolioModule } from '../entity-portfolio/entity-portfolio.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Asset.name, schema: AssetSchema },
    ]),
    UtilsModule,
    EntityPortfolioModule
  ],
  controllers: [AssetController],
  providers: [AssetService, ErrorService],
  exports: [AssetService]
})
export class AssetModule {}
