apiVersion: apps/v1
kind: Deployment
metadata:
  name: entity-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: entity-service
  template:
    metadata:
      labels:
        app: entity-service
    spec:
      containers:
        - name: entity-service
          image: REGION-docker.pkg.dev/GKE_PROJECT/REPOSITORY/DEPLOYMENT_NAME:SHORT_SHA
          ports:
          - containerPort: 3000
          # ... other container configuration
          env:
          - name: MONGO_URL
            valueFrom:
              secretKeyRef:
                name: entity-service
                key: mongo-url
          - name: SWAGGER_PASSWORD
            valueFrom:
              secretKeyRef:
                name: entity-service
                key: swagger-password
          - name: CONFIG_SERVICE_BASE_URL
            valueFrom:
              secretKeyRef:
                name: entity-service
                key: config-service-base-url
          