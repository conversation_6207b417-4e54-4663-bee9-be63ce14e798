import { Body, Controller, Post } from '@nestjs/common';
import { UpsertService } from './upsert.service';
import { UpsertParams } from './interface/upsert.interface';

@Controller('upsert')
export class UpsertController {
  constructor(private readonly upsertService: UpsertService) {}

  @Post()
  async upsert(@Body() upsertParams: UpsertParams) {
    try {
      if (!upsertParams.resource) {
        throw new Error('Resource is required');
      }
      if (!upsertParams.data) {
        throw new Error('Data is required');
      }
      if (!['insert', 'update'].includes(upsertParams.operation)) {
        throw new Error('Invalid operation type');
      }

      const result = await this.upsertService.upsert(
        upsertParams.resource,
        upsertParams.operation,
        upsertParams.data,
        upsertParams.where
      );
      return result;
    } catch (error) {
      throw error;
    }
  }
}
