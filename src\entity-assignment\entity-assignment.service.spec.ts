import { Test, TestingModule } from '@nestjs/testing';
import { EntityAssignmentService } from './entity-assignment.service';
import { getModelToken } from '@nestjs/mongoose';
import { EntityAssignment } from './schema/entity-assignment.schema';
import { EntityPortfolio } from '../entity-portfolio/schema/entity-portfolo.schema';
import { ErrorService } from '../error/error.service';
import { EntityPortfolioService } from '../entity-portfolio/entity-portfolio.service';
import { Model } from 'mongoose';
import { CreateEntityAssignmentDto } from './dto/entity-assignment.dto';
import { Types } from 'mongoose';
import { EntityType } from './enum/entity-assignment.enum';

describe('EntityAssignmentService', () => {
  let service: EntityAssignmentService;
  let mockEntityAssignmentModel: any;
  let mockEntityPortfolioModel: any;
  let mockErrorService: any;
  let mockEntityPortfolioService: any;

  const mockAssignment = {
    organization_id: new Types.ObjectId(),
    assigned_role_id: 'test-role-id', // String as per DTO
    assigned_id: 'test-assigned-id',
    assigned_to_id: 'test-assigned-to-id',
    assigned_to_role_id: 'test-role-id', // String as per DTO
    program_id: new Types.ObjectId(),
    assigned_to_entity_type: EntityType.USER,
    delete_flag: 0
  };

  beforeEach(async () => {
    mockEntityAssignmentModel = {
      create: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
    };

    mockEntityPortfolioModel = {
      findOne: jest.fn(),
      find: jest.fn(),
    };

    mockErrorService = {
      serverError: jest.fn(),
    };

    mockEntityPortfolioService = {
      findOne: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntityAssignmentService,
        {
          provide: getModelToken(EntityAssignment.name),
          useValue: mockEntityAssignmentModel,
        },
        {
          provide: getModelToken(EntityPortfolio.name),
          useValue: mockEntityPortfolioModel,
        },
        {
          provide: ErrorService,
          useValue: mockErrorService,
        },
        {
          provide: EntityPortfolioService,
          useValue: mockEntityPortfolioService,
        },
      ],
    }).compile();

    service = module.get<EntityAssignmentService>(EntityAssignmentService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createEntityAssignment', () => {
    it('should create an entity assignment successfully', async () => {
      const payload: CreateEntityAssignmentDto = mockAssignment;
      const createdAssignment = { ...mockAssignment, id: new Types.ObjectId() };
      
      mockEntityAssignmentModel.create.mockResolvedValueOnce(createdAssignment);

      const result = await service.createEntityAssignment(payload);

      expect(result).toEqual({
        message: 'Entity assignment created successfully',
        status: 'success',
        data: createdAssignment
      });
      expect(mockEntityAssignmentModel.create).toHaveBeenCalledWith(payload);
    });

    it('should handle errors during creation', async () => {
      const payload: CreateEntityAssignmentDto = mockAssignment;
      const error = new Error('Database error');
      
      mockEntityAssignmentModel.create.mockRejectedValueOnce(error);
      mockErrorService.serverError.mockReturnValueOnce(undefined);

      const result = await service.createEntityAssignment(payload);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith(error.message);
    });
  });

  describe('getStaffAssignment', () => {
    const mockUserId = 'test-user-id';
    const mockRoleId = 'test-role-id';
    const mockTenantId = 'test-tenant-id';
    const mockProgramId = 'test-program-id';

    it('should get staff assignment successfully', async () => {
      const mockStaffAssignment = {
        ...mockAssignment,
        user_id: mockUserId,
        role_id: mockRoleId,
        tenant_id: mockTenantId,
        program_id: mockProgramId
      };

      mockEntityAssignmentModel.findOne.mockResolvedValueOnce(mockStaffAssignment);

      const result = await service.getStaffAssignment(
        mockUserId,
        mockRoleId,
        mockTenantId,
        mockProgramId
      );

      expect(result).toEqual({
        message: 'Staff assignment retrieved successfully',
        status: 'success',
        data: mockStaffAssignment
      });
      expect(mockEntityAssignmentModel.findOne).toHaveBeenCalledWith({
        user_id: mockUserId,
        role_id: mockRoleId,
        tenant_id: mockTenantId,
        program_id: mockProgramId
      });
    });

    it('should return null data when no assignment is found', async () => {
      mockEntityAssignmentModel.findOne.mockResolvedValueOnce(null);

      const result = await service.getStaffAssignment(
        mockUserId,
        mockRoleId,
        mockTenantId,
        mockProgramId
      );

      expect(result).toEqual({
        message: 'Staff assignment retrieved successfully',
        status: 'success',
        data: null
      });
      expect(mockEntityAssignmentModel.findOne).toHaveBeenCalledWith({
        user_id: mockUserId,
        role_id: mockRoleId,
        tenant_id: mockTenantId,
        program_id: mockProgramId
      });
    });

    it('should handle errors during staff assignment retrieval', async () => {
      const error = new Error('Database error');
      mockEntityAssignmentModel.findOne.mockRejectedValueOnce(error);
      mockErrorService.serverError.mockReturnValueOnce(undefined);

      const result = await service.getStaffAssignment(mockUserId, mockRoleId, mockTenantId, mockProgramId);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith(error.message);
    });
  });

  describe('getAssignmentsByProgram', () => {
    it('should get assignments by program successfully', async () => {
      const mockProgramId = 'test-program-id';
      const mockAssignments = [mockAssignment];
      const mockLastSyncTime = '2023-01-01';

      mockEntityAssignmentModel.find.mockResolvedValueOnce(mockAssignments);

      const result = await service.getAssignmentsByProgram(mockProgramId, mockLastSyncTime);

      expect(result).toEqual({
        message: 'Assignments gotten successfully',
        status: 'success',
        data: mockAssignments
      });
      expect(mockEntityAssignmentModel.find).toHaveBeenCalledWith({
        program_id: mockProgramId,
        updatedAt: { $gte: expect.any(Date) }
      });
    });

    it('should handle invalid last_sync_time', async () => {
      const mockProgramId = 'test-program-id';
      const invalidLastSyncTime = 'invalid-date';
      const error = new Error('Invalid date');

      mockEntityAssignmentModel.find.mockRejectedValueOnce(error);
      mockErrorService.serverError.mockReturnValueOnce(undefined);

      const result = await service.getAssignmentsByProgram(mockProgramId, invalidLastSyncTime);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith(error.message);
    });

    it('should get assignments without last_sync_time', async () => {
      const mockProgramId = 'test-program-id';
      const mockAssignments = [mockAssignment];

      mockEntityAssignmentModel.find.mockResolvedValueOnce(mockAssignments);

      const result = await service.getAssignmentsByProgram(mockProgramId);

      expect(result).toEqual({
        message: 'Assignments gotten successfully',
        status: 'success',
        data: mockAssignments
      });
      expect(mockEntityAssignmentModel.find).toHaveBeenCalledWith({
        program_id: mockProgramId,
        updatedAt: { $gte: expect.any(Date) }
      });
    });
  });
});
