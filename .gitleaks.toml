title = "Enhanced GitLeaks Configuration"

[extend]
useDefault = true

# Rule to detect AWS Access Key IDs
[[rules]]
id = "aws-access-key-id"
description = "AWS Access Key ID"
regex = '''AKIA[0-9A-Z]{16}'''
tags = ["key", "aws"]
keywords = ["AKIA"]

# Rule to detect AWS Secret Access Keys
[[rules]]
id = "aws-secret-access-key"
description = "AWS Secret Access Key"
regex = '''(?i)aws.*['"][0-9a-zA-Z/+]{40}['"]'''
tags = ["key", "aws"]
keywords = ["aws"]

# Rule to detect Generic API keys
[[rules]]
id = "generic-api-key"
description = "Generic API Key"
regex = '''(?i)(api[_-]?key|secret|token|password|access[_-]?token)[\s]*[:=][\s]*['|"]?[0-9a-zA-Z]{20,}['|"]?'''
tags = ["key", "api", "secret"]
keywords = ["api", "key", "secret", "token", "password", "access"]

# Rule to detect GitHub tokens
[[rules]]
id = "github-token"
description = "GitHub Token"
regex = '''gh[pousr]_[A-Za-z0-9_]{36}'''
tags = ["key", "github"]
keywords = ["ghp", "gho", "ghu", "ghs", "ghr"]

# Rule to detect Slack Webhook URLs
[[rules]]
id = "slack-webhook-url"
description = "Slack Webhook URL"
regex = '''https://hooks.slack.com/services/T[a-zA-Z0-9_]+/B[a-zA-Z0-9_]+/[a-zA-Z0-9_]+'''
tags = ["url", "webhook", "slack"]
keywords = ["slack", "hooks"]

# Rule to detect private keys
[[rules]]
id = "private-key"
description = "Private Key"
regex = '''-----BEGIN (RSA|DSA|EC|OPENSSH|PRIVATE) KEY-----'''
tags = ["key", "private"]
keywords = ["BEGIN", "PRIVATE", "KEY"]

# Global allowlist
[allowlist]
description = "Global allow list"
paths = [
  '''sonar-project.properties''',
  '''\.github/workflows/.*\.yaml'''
]

regexes = [
  '''(?i)(sonar\.projectKey=|sonar\.sources=|sonar\.javascript\.lcov\.reportPaths=).*'''
]