import { Injectable } from '@nestjs/common';
import { Model, Mongoose, Types } from 'mongoose';
import { User, UserDocument } from './schema/user.schema';
import { InjectModel } from '@nestjs/mongoose';
import {
  CreateUserDto,
  RequestParticipantsDTO,
  RequestRegisteredDTO,
} from './dto/users.dto';
import { ErrorService } from '../error/error.service';
import { PayloadUtils } from '../utils/payload.utils';
import { CaseConverterUtil } from '../utils/case-converter.util';
import { EntityPortfolioService } from '../entity-portfolio/entity-portfolio.service';
import { Cron } from '@nestjs/schedule';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import { ComparisonOperator } from './enum/user.enum';
import { getLastSyncTime } from '../utils/last_sync.utils';
import { AssetService } from '../asset/asset.service';
import { BadgeDto } from './dto/badge.dto';

@Injectable()
export class UsersService {
  private cronStatus: {
    last_sync_time: Date;
  };

  private readonly cronStatusPath = path.join(
    process.cwd(),
    'src',
    'users',
    'cron-status.json',
  );

  private loadCronStatus() {
    try {
      const data = fs.readFileSync(this.cronStatusPath, 'utf8');
      const status = JSON.parse(data);
      this.cronStatus = {
        ...status,
        last_sync_time: new Date(status.last_sync_time),
      };
    } catch (error) {
      // If file doesn't exist or is invalid, use default values
      this.cronStatus = {
        last_sync_time: new Date('2022/01/14'),
      };
      this.saveCronStatus();
    }
  }

  private saveCronStatus() {
    const statusToSave = {
      last_sync_time: this.cronStatus.last_sync_time.toISOString(),
    };
    try {
      const dir = path.dirname(this.cronStatusPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      fs.writeFileSync(
        this.cronStatusPath,
        JSON.stringify(statusToSave, null, 2),
      );
    } catch (error) {
      console.error('Failed to save cron status:', error);
    }
  }
  entityPortfolioModel: any;
  constructor(
    @InjectModel(User.name)
    private readonly userModel: Model<UserDocument>,
    private readonly errorService: ErrorService,
    private readonly payloadUtils: PayloadUtils,
    private readonly caseConverterUtil: CaseConverterUtil,
    private readonly entityPortfolioService: EntityPortfolioService,
    private readonly assetService: AssetService,
  ) {
    this.loadCronStatus();
  }

  /**
   * Creates or updates a user based on the payload
   * @param payload - The payload containing the details of the user
   * @returns The created or updated user
   */
  async createUser(
    payloads: CreateUserDto[],
  ): Promise<UserDocument[] | undefined> {
    try {
      const results: UserDocument[] = [];

      for (const payload of payloads) {
        const performer = await this.userModel.findOne({
          performer_id: payload.performer_id,
          delete_flag: 0,
        });

        payload.home_office = performer?.home_office||{};
        const transformedPayload = this.payloadUtils.transformPayload(payload);

        // If id exists create or update based off the id
        if ('id' in transformedPayload && transformedPayload.id) {
          const data = await this.userModel.findOne({
            id: transformedPayload.id,
            delete_flag: 0,
          });

          if (data) {
            const result = await this.userModel.findOneAndUpdate(
              { id: transformedPayload.id, delete_flag: 0 },
              transformedPayload,
              { new: true, upsert: true, setDefaultsOnInsert: true },
            );
            results.push(result);
            continue;
          }

          const newUser = await this.userModel.create(transformedPayload);
          results.push(newUser);
          continue;
        }

        //if the response_id already exists just update the exsisting record
        if (payload.response_id) {
          const data = await this.userModel.findOne({
            response_id: payload.response_id,
            delete_flag: 0,
          });
          if (data) {
            const result = await this.userModel.findOneAndUpdate(
              { response_id: payload.response_id, delete_flag: 0 },
              transformedPayload,
              { new: true, upsert: true, setDefaultsOnInsert: true },
            );
            results.push(result);
            continue;
          }
        }

        // If the role of the person carrying out the activity matches the role of the target user

        // if (payload.role === payload.entity[0]) {
        if (payload.unique_entity_id && payload.entity[0] == payload.role) {
          const data = await this.userModel.findOne({
            id: payload.unique_entity_id,
            delete_flag: 0,
          });

          if (data) {
            const result = await this.userModel.findOneAndUpdate(
              { id: payload.unique_entity_id, delete_flag: 0 },
              transformedPayload,
              { new: true, upsert: true, setDefaultsOnInsert: true },
            );
            results.push(result);
            continue;
          }

          if (!('id' in transformedPayload)) {
            const newId = this.payloadUtils.generateId();
            const newUser = await this.userModel.create({
              ...transformedPayload,
              _id: newId,
              id: newId,
            });
            results.push(newUser);
            continue;
          }

          const newUser = await this.userModel.create(transformedPayload);
          results.push(newUser);
          continue;
        } else {
          if (!('id' in transformedPayload)) {
            const newId = this.payloadUtils.generateId();
            const newUser = await this.userModel.create({
              ...transformedPayload,
              _id: newId,
              id: newId,
            });
            results.push(newUser);
            continue;
          }
        }
        // } else {
        //   if (!('id' in transformedPayload)) {
        //     const newId = this.payloadUtils.generateId();
        //     const newUser = await this.userModel.create({
        //       ...transformedPayload,
        //       _id: newId,
        //       id: newId,
        //     });
        //     results.push(newUser);
        //     continue;
        //   }
        //   const newUser = await this.userModel.create(transformedPayload);
        //   results.push(newUser);
        //   continue;
        // }
      }
      return results;
    } catch (error: any) {
      console.log(error);
      this.errorService.serverError('Something went wrong');
      return undefined;
    }
  }

  /**
   * Gets a user by ID
   * @param id - The ID of the user
   * @returns The user with the specified ID
   */
  async getUserById(id: string, last_sync_time?: string) {
    try {
      const last_sync = getLastSyncTime(last_sync_time);
      return await this.userModel.findOne({
        id,
        delete_flag: 0,
        updatedAt: { $gte: last_sync },
      });
    } catch (error: any) {
      this.errorService.serverError(error.message);
      return undefined;
    }
  }

  /**
   * Gets all users in an organization
   * @param organizationId - The ID of the organization
   * @returns An array of users in the organization
   */
  async getAllUsers(organizationId: string, last_sync_time?: string) {
    try {
      const last_sync = getLastSyncTime(last_sync_time);
      return await this.userModel.find({
        organization_id: new Types.ObjectId(organizationId),
        delete_flag: 0,
        updatedAt: { $gte: last_sync },
      });
    } catch (error: any) {
      this.errorService.serverError(error.message);
      return undefined;
    }
  }

  /**
   * Gets the assignment of a user
   * @param user_id - The ID of the user
   * @param organization_id - The ID of the organization
   * @param role_id - The ID of the role
   * @param program_id - The ID of the program
   * @returns The assignment of the user
   */
  async getUserAssignment(
    user_id: string,
    organization_id: string,
    role_id: string,
    program_id: string,
    last_sync_time?: string,
  ) {
    try {
      const assignment_data =
        await this.entityPortfolioService.getStaffPortfolio(
          user_id,
          role_id,
          organization_id,
          program_id,
        );
      if (!assignment_data) {
        return [];
      }

      const portfolio = assignment_data.data[0].portfolio;

      const ids = portfolio.map((item: any) => item.id);

      const users = await this.userModel.find({
        id: { $in: ids },
        delete_flag: 0,
        organization_id,
        updatedAt: { $gte: last_sync_time },
      });
      return {
        message: 'users gotten successfully',
        status: 'success',
        data: users,
      };
    } catch (error: any) {
      this.errorService.serverError(error.message);
      return undefined;
    }
  }

  /**
   * Gets all users
   * @returns An array of users
   */
  async fetchAll() {
    try {
      const data = await this.userModel.find({ delete_flag: 0 });
      return data;
    } catch (err) {
      console.log(err.message);
      this.errorService.serverError(err.message);
      return [];
    }
  }

  async getRegisteredUsers(last_sync_time: Date) {
    try {
      const response = await axios.get(
        `${process.env.IAM_SERVICE_BASE_URL}/users/active?last_sync_time=${last_sync_time.toISOString()}`,
      );
      const data = response.data.data;
      return data;
    } catch (err) {
      console.log(err.message);
      this.errorService.serverError(err.message);
      return [];
    }
  }

  async createEvent(event: any) {
    try {
      const result = await axios.post(
        `${process.env.BADGE_ASSIGNMENT_SERVICE_BASE_URL}/events`,
        event,
      );
      return result.data;
    } catch (err) {
      console.log('Error triggering event is', err.message);
    }
  }

  @Cron('*/2 * * * *')
  async migrateUsers() {
    try {
      const currentTime = new Date();
      console.log('user migration starting');
      const users = await this.getRegisteredUsers(
        this.cronStatus.last_sync_time,
      );

      if (users.length === 0) {
        return;
      }

      const inserted: any[] = [];
      const updated: any[] = [];
      const allEvents: any[] = [];

      for (const user of users) {
        if (user.updated_at < this.cronStatus.last_sync_time) {
          continue;
        }
        user.delete_flag = 0;
        user.organization_id = user.tenantId;
        user.role_ids = user.program?.length
          ? user.program.map((p: any) => p.role.id)
          : [];
        user.program_ids = user.program?.length
          ? user.program.map((p: any) => p.programId)
          : [];

        delete user.tenantId;
        delete user.updatedAt;
        delete user.createdAt;
        delete user.role;
        delete user.program;

        const convertedUser =
          this.caseConverterUtil.convertKeysToSnakeCase(user);

        // Check if user exists
        const existingUser = await this.userModel.findOne({ id: user.id });
        if (existingUser) {
        }

        // Upsert user
        await this.userModel.findOneAndUpdate(
          { id: user.id },
          {
            $set: {
              ...convertedUser,
            },
          },
          { upsert: true, new: true },
        );

        if (existingUser) {
          if (
            (existingUser.program_ids?.length || 0) !==
            (user.program_ids?.length || 0)
          ) {
            updated.push(user);
          }
        } else {
          inserted.push(user);
        }
      }

      // Get the events
      for (let user of updated) {
        for (let i = 0; i < (user.program_ids?.length || 0); i++) {
          if (user.role_ids == null || i >= user.role_ids.length) {
            continue;
          }
          allEvents.push({
            organization_id: user.organization_id,
            entity_id: user.id,
            program_id: user.program_ids[i],
            entity_role_id: user.role_ids[i],
            event_type: 'program_participation',
            program_participated_id: user.program_ids[i],
          });
        }
      }

      for (let user of inserted) {
        allEvents.push({
          organization_id: user.organization_id,
          entity_id: user.id,
          program_id: user?.program_ids?.[0],
          entity_role_id: user?.role_ids?.[0],
          event_type: 'date_of_registration',
          program_participated_id: user.program_ids[0],
          registration_date: new Date().toISOString(),
        });

        for (let i = 0; i < (user.program_ids?.length || 0); i++) {
          if (user.role_ids == null || i >= user.role_ids.length) {
            continue;
          }
          allEvents.push({
            organization_id: user.organization_id,
            entity_id: user.id,
            program_id: user.program_ids[i],
            entity_role_id: user.role_ids[i],
            event_type: 'program_participation',
            program_participated_id: user.program_ids[i],
          });
        }
      }

      this.createEvent(allEvents);

      console.log('User migration completed.');
      console.log('Total Events triggered:', allEvents.length);
      console.log('Inserted users:', inserted.length, inserted);
      console.log(
        'Existing Users with Program Change:',
        updated.length,
        updated,
      );
      console.log('Total users migrated:', users.length);

      this.cronStatus.last_sync_time = currentTime;
      this.saveCronStatus();
    } catch (error: any) {
      console.log(error.message);
      this.errorService.serverError(error.message);
    }
  }

  /**
   * Gets the list of user Ids that participate in specified program-role combinations
   * @param payload - A list of program,role,organization id objects
   * @returns A list of program, role,organization id  and participating user ids
   */
  async getProgramParticipants(payload: RequestParticipantsDTO[]) {
    try {
      const results = await Promise.all(
        payload.map(async (item) => {
          const users = await this.userModel.find(
            {
              role_ids: { $in: [item.entity_role_id] },
              program_ids: { $in: [item.program_id] },
              organization_id: item.organization_id,
            },
            { id: 1, _id: 0 },
          );
          return {
            ...item,
            entity_ids: users.map((user) => user.id),
          };
        }),
      );
      return {
        message: 'Participants gotten successfully',
        status: 'success',
        data: results,
      };
    } catch (error: any) {
      console.log(error);
      this.errorService.serverError('Something went wrong');
      return undefined;
    }
  }

  /**
   * Gets the list of user Ids that were registered relative to a given date and comparator
   * @param payload - A list of dates and comparison operator
   * @returns A list of dates and comparison operator and corresponding user ids
   */
  async getRegisteredIds(payload: RequestRegisteredDTO[]) {
    try {
      const results = await Promise.all(
        payload.map(async (item) => {
          const filter =
            item.comparison_operator == ComparisonOperator.EARLIER_THAN
              ? { createdAt: { $lt: new Date(item.onboarding_date) } }
              : { createdAt: { $gt: new Date(item.onboarding_date) } };
          const users = await this.userModel.find(filter, { id: 1, _id: 0 });
          return {
            ...item,
            entity_ids: users.map((user) => user.id),
          };
        }),
      );
      return {
        message: 'Registered entities gotten successfully',
        status: 'success',
        data: results,
      };
    } catch (error: any) {
      console.log(error);
      this.errorService.serverError('Something went wrong');
      return undefined;
    }
  }

  async getProgramEntities(program_id: string, organization_id: string) {
    try {
      console.log(program_id, organization_id);
      const users = await this.userModel.find({
        program_ids: { $in: [new Types.ObjectId(program_id)] },
        organization_id: new Types.ObjectId(organization_id),
        delete_flag: 0,
      });
      const assets =
        (await this.assetService.getProgramAssets(
          program_id,
          organization_id,
        )) || [];

      const entities = [...(users || []), ...assets];
      console.log(entities);

      return {
        message: 'Program entities gotten successfully',
        status: 'success',
        data: entities,
      };
    } catch (error: any) {
      console.log(error);
      this.errorService.serverError('Something went wrong');
      return undefined;
    }
  }

  async upsertBadge(payload: BadgeDto) {
    try {
      const user = await this.userModel.findOne({
        id: payload.id,
        organization_id: payload.organization_id,
      });
      if (!user) {
        return this.errorService.notFoundError('User not found');
      }
      user.badge_id = payload.badge_id;
      user.updatedAt = new Date();
      await user.save();
      return {
        message: 'Badge updated successfully',
        status: 'success',
        data: user,
      };
    } catch (error: any) {
      console.log(error.message);
      this.errorService.serverError('Something went wrong');
      return [];
    }
  }
}
