import { Test, TestingModule } from '@nestjs/testing';
import { UpsertController } from './upsert.controller';
import { UpsertService } from './upsert.service';
import { UpsertParams } from './interface/upsert.interface';

describe('UpsertController', () => {
  let controller: UpsertController;
  let mockUpsertService: jest.Mocked<UpsertService>;

  beforeEach(async () => {
    mockUpsertService = {
      upsert: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UpsertController],
      providers: [
        {
          provide: UpsertService,
          useValue: mockUpsertService,
        },
      ],
    }).compile();

    controller = module.get<UpsertController>(UpsertController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('upsert', () => {
    it('should call upsertService.upsert with correct parameters for insert operation', async () => {
      const params: UpsertParams = {
        resource: 'test-collection',
        operation: 'insert',
        data: [{ name: 'test' }]
      };

      mockUpsertService.upsert.mockResolvedValue({ upsertedCount: 1 });

      await controller.upsert(params);

      expect(mockUpsertService.upsert).toHaveBeenCalledWith(
        params.resource,
        params.operation,
        params.data,
        undefined
      );
    });

    it('should call upsertService.upsert with correct parameters for update operation', async () => {
      const params: UpsertParams = {
        resource: 'test-collection',
        operation: 'update',
        data: [{ name: 'test' }],
        where: [{ id: '123' }]
      };

      mockUpsertService.upsert.mockResolvedValue({ modifiedCount: 1 });

      await controller.upsert(params);

      expect(mockUpsertService.upsert).toHaveBeenCalledWith(
        params.resource,
        params.operation,
        params.data,
        params.where
      );
    });

    it('should handle invalid operation type', async () => {
      const params = {
        resource: 'test-collection',
        operation: 'invalid' as any,
        data: [{ name: 'test' }]
      };

      await expect(controller.upsert(params)).rejects.toThrow('Invalid operation type');
    });

    it('should handle missing resource', async () => {
      const params = {
        operation: 'insert',
        data: [{ name: 'test' }]
      } as any;

      await expect(controller.upsert(params)).rejects.toThrow('Resource is required');
    });

    it('should handle missing data', async () => {
      const params = {
        resource: 'test-collection',
        operation: 'insert'
      } as any;

      await expect(controller.upsert(params)).rejects.toThrow('Data is required');
    });

    it('should handle service errors', async () => {
      const params: UpsertParams = {
        resource: 'test-collection',
        operation: 'update',
        data: [{ name: 'test' }],
        where: [{ id: '123' }]
      };

      const error = new Error('Test error');
      mockUpsertService.upsert.mockRejectedValue(error);

      await expect(controller.upsert(params)).rejects.toThrow(error);
    });
  });
});
