import { ErrorService } from '../error/error.service';
import axios from 'axios';
import { EntityType } from '../entity-assignment/enum/entity-assignment.enum';
import { InjectModel } from '@nestjs/mongoose';
import {
  EntityAssignment,
  EntityAssignmentDocument,
} from '../entity-assignment/schema/entity-assignment.schema';
import {
  EntityPortfolio,
  EntityPorfolioDocument,
} from './schema/entity-portfolo.schema';
import { Model, Types } from 'mongoose';
import { getLastSyncTime } from '../utils/last_sync.utils';

export class EntityPortfolioService {
  // entityAssignmentModel: any;
  constructor(
    private readonly errorService: ErrorService,
    @InjectModel(EntityAssignment.name)
    private readonly entityAssignmentModel: Model<EntityAssignmentDocument>,
    @InjectModel(EntityPortfolio.name)
    private readonly entityPortfolioModel: Model<EntityPorfolioDocument>,
  ) {}

  async buildPortfolio(
    targetUserId: string,
    roleId: string,
    organization_id: string,
    program_id: string,
    processedSet: Set<string> = new Set(),
  ) {
    try {
      if (processedSet.has(targetUserId)) {
        return [];
      }
      processedSet.add(targetUserId);

      const assets = await this.entityAssignmentModel.find({
        assigned_to_id: targetUserId,
        delete_flag: 0,
        assigned_to_entity_type: EntityType.ASSET,
      });

      const assetPortfolios = assets.map((asset) => ({
        id: asset.assigned_id,
        role_id: roleId,
      }));

      const subordinateRoles = await this.getChildPortfolio(roleId);

      if (subordinateRoles.length === 0) {
        return [];
      }

      const user = [{id: targetUserId, role_id:roleId}]

      const directSubordinate = subordinateRoles[0];

      const directAssignments = await this.entityAssignmentModel.find({
        assigned_role_id: new Types.ObjectId(directSubordinate),
        delete_flag: 0,
        assigned_to_entity_type: EntityType.USER,
        assigned_to_id: targetUserId,
        assigned_to_role_id: new Types.ObjectId(roleId),
      });

      const portfolioPromises = directAssignments.map(async (assignment) => {

        const subPortfolios = await this.buildPortfolio(
          assignment.assigned_id,
          assignment.assigned_role_id.toString(),
          organization_id,
          program_id,
          processedSet,
        );

        processedSet.delete(targetUserId);
        return subPortfolios;
      });

      const resolvedPortfolios = await Promise.all(portfolioPromises);
      const subordinatePortfolios = resolvedPortfolios.flat();

      const finalPortfolio = [...user ,...assetPortfolios, ...subordinatePortfolios];

      await this.entityPortfolioModel.findOneAndUpdate(
        { id: targetUserId, role_id: new Types.ObjectId(roleId) },
        {
          id: targetUserId,
          role_id: new Types.ObjectId(roleId),
          is_valid: 1,
          portfolio: finalPortfolio,
          updated_at: new Date(),
          organization_id,
          program_id,
        },
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true,
        },
      );
      return finalPortfolio;
    } catch (error) {
      console.log(error);
      this.errorService.serverError(error.message);
    }
  }
  private async getChildPortfolio(role_id: string) {
    const response = await axios.get(
      `${process.env.CONFIG_SERVICE_BASE_URL}/role/child_portfolios/${role_id}`,
    );
    return response.data;
  }

  async getStaffPortfolio(
    user_id: string,
    role_id: string,
    organization_id: string,
    program_id: string,
    last_sync_time?: string,
  ): Promise<any> {
    try {
      const last_sync= getLastSyncTime(last_sync_time);
      if (!user_id || !organization_id || !role_id || !program_id) {
        return { message: 'Invalid parameters', status: 'error' };
      }
      const entityPortfolio = await this.entityPortfolioModel.find({
        id: user_id,
        role_id: role_id,
        is_valid: 1,
        organization_id: organization_id,
        program_id: program_id,
        updated_at: { $gte: last_sync },
      });
      if (entityPortfolio.length > 0) {
        return {
          message: 'Entity portfolio gotten successfully',
          status: 'success',
          data: entityPortfolio,
        };
      }
      await this.buildPortfolio(user_id, role_id, organization_id, program_id);
      return {
        message: 'Entity portfolio gotten successfully',
        status: 'success',
        data: await this.entityPortfolioModel.find({
          id: user_id,
          role_id: new Types.ObjectId(role_id),
          is_valid: 1,
        }),
      };
    } catch (error) {
      console.log(error);
      this.errorService.serverError(error.message);
    }
  }

  async getStaffPortfolioByAssignment(
    id: string,
    role_id: string,
    organization_id: string,
    program_id: string,
    last_sync_time?: string,
  ) {
    try {
      const assignment_data = await this.getStaffPortfolio(
        id,
        role_id,
        organization_id,
        program_id,   
        last_sync_time
      );
      
      const portfolio = assignment_data?.data?.[0]?.portfolio || [];

      const ids = portfolio.map((item: any) => item.id);

      const portfolios = await this.entityPortfolioModel.find({
        id: { $in: ids },
        is_valid: 1,
        program_id: new Types.ObjectId(program_id)
      });

      return {
        message: 'Entity portfolio gotten successfully',
        status: 'success',
        data: portfolios,
      };
    } catch (error: any) {
      console.log("error is",error)
      this.errorService.serverError(error.message);
    }
  }
}
