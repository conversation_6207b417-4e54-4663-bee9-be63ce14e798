import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Asset } from './schema/asset.schema';
import { ErrorService } from '../error/error.service';
import { PayloadUtils } from '../utils/payload.utils';
import { EntityPortfolioService } from '../entity-portfolio/entity-portfolio.service';
import { Types } from 'mongoose';
import { AssetService } from './asset.service';
import { CreateAssetDto } from './dto/asset.dto';
import { payload } from './mocks/mock-values.mock';

describe('AssetService', () => {
  let service: AssetService;
  let mockAssetModel: any;
  let mockErrorService: any;
  let mockPayloadUtils: any;
  let mockEntityPortfolioService: any;
  let testPayload: CreateAssetDto;

  beforeEach(async () => {
    jest.clearAllMocks();

    mockErrorService = {
      serverError: jest.fn().mockImplementation(() => undefined),
      badRequestError: jest.fn().mockImplementation(() => undefined),
    };

    mockPayloadUtils = {
      transformPayload: jest.fn(),
      generateId: jest.fn().mockReturnValue('generated-id'),
    };

    mockEntityPortfolioService = {
      getEntityPortfolioByEntityId: jest.fn(),
    };

    mockAssetModel = {
      find: jest.fn(),
      findOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
      create: jest.fn(),
    };

    testPayload = {
      role: new Types.ObjectId('507f1f77bcf86cd799439012'),
      entity: [new Types.ObjectId('507f1f77bcf86cd799439013')],
      organization_id: new Types.ObjectId('507f1f77bcf86cd799439011'),
      program_ids: [new Types.ObjectId('507f1f77bcf86cd799439013')],
      badge_ids: [new Types.ObjectId('507f1f77bcf86cd799439013')],
      response: [
        {
          id: 'screen-1',
          sections: [
            {
              id: 'section-1',
              components: [
                {
                  id: 'component-1',
                  label: 'name',
                  answer: 'Entity1',
                },
                {
                  id: 'component-2',
                  label: 'value',
                  answer: 'Value1',
                },
              ],
            },
          ],
        },
      ],
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AssetService,
        {
          provide: getModelToken(Asset.name),
          useValue: mockAssetModel,
        },
        {
          provide: PayloadUtils,
          useValue: mockPayloadUtils,
        },
        {
          provide: ErrorService,
          useValue: mockErrorService,
        },
        {
          provide: EntityPortfolioService,
          useValue: mockEntityPortfolioService,
        },
      ],
    }).compile();

    service = module.get<AssetService>(AssetService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createAsset', () => {
    it('should update existing asset when id exists and asset is found', async () => {
      const payloadWithId = new CreateAssetDto();
      Object.assign(payloadWithId, {
        id: 'existing-id',
        ...payload
      });

      mockPayloadUtils.transformPayload.mockReturnValueOnce(payloadWithId);
      mockAssetModel.findOne.mockResolvedValueOnce(payloadWithId);
      mockAssetModel.findOneAndUpdate.mockResolvedValueOnce(payloadWithId);

      const result = await service.createAsset([payloadWithId] as CreateAssetDto[]);
      
      expect(mockAssetModel.findOne).toHaveBeenCalledWith({ 
        id: 'existing-id', 
        delete_flag: 0 
      });
      expect(mockAssetModel.findOneAndUpdate).toHaveBeenCalled();
      expect(result).toEqual([payloadWithId]);
    });

    it('should create new asset when role matches entity and unique_entity_id exists', async () => {
      const payloadWithMatchingRole = {
        ...testPayload,
        role: testPayload.entity[0],
        unique_entity_id: 'test-entity-id',
      };
      const expectedAsset = {
        ...payloadWithMatchingRole,
        _id: 'generated-id',
        id: 'generated-id',
      };

      mockPayloadUtils.transformPayload.mockReturnValueOnce(payloadWithMatchingRole);
      mockAssetModel.findOne.mockResolvedValueOnce(null);
      mockPayloadUtils.generateId.mockReturnValueOnce('generated-id');
      mockAssetModel.create.mockResolvedValueOnce(expectedAsset);

      const result = await service.createAsset([payloadWithMatchingRole] as CreateAssetDto[]);

      expect(mockAssetModel.findOne).toHaveBeenCalledWith({
        id: 'test-entity-id',
        delete_flag: 0,
      });
      expect(mockAssetModel.create).toHaveBeenCalledWith(expectedAsset);
      expect(result).toEqual([expectedAsset]);
    });

    it('should throw bad request error when unique_entity_id is missing for matching role', async () => {
      const testPayload = new CreateAssetDto();
      Object.assign(testPayload, {
        ...payload,
        entity: payload.role,
        unique_entity_id: ''
      });

      try {
        await service.createAsset([testPayload] as CreateAssetDto[]);
      } catch (error) {
        expect(mockErrorService.badRequestError).toHaveBeenCalledWith('asset with unique_entity_id is required');
      }
    });

    it('should create new asset with generated id when no id exists', async () => {
      const payloadWithoutId = {
        ...testPayload,
        role: new Types.ObjectId(), // Different from entity to avoid unique_entity_id check
      };
      mockPayloadUtils.transformPayload.mockReturnValueOnce(payloadWithoutId);
      mockPayloadUtils.generateId.mockReturnValueOnce('generated-id');
      mockAssetModel.create.mockResolvedValueOnce({
        ...payloadWithoutId,
        _id: 'generated-id',
        id: 'generated-id'
      });

      const result = await service.createAsset([payloadWithoutId]);
      
      expect(mockPayloadUtils.generateId).toHaveBeenCalled();
      expect(mockAssetModel.create).toHaveBeenCalledWith({
        ...payloadWithoutId,
        _id: 'generated-id',
        id: 'generated-id'
      });
      expect(result).toBeDefined();
      expect(result![0]).toEqual({
        ...payloadWithoutId,
        _id: 'generated-id',
        id: 'generated-id'
      });
    });

    it('should create asset with new ID when id is not in transformedPayload', async () => {
      const testPayload = {
        organization_id: new Types.ObjectId(),
        response_id: 'resp-1',
        unique_entity_id: 'unique-1',
        role: new Types.ObjectId(),
        entity: [new Types.ObjectId()],
        program_ids: [new Types.ObjectId()],
        response: [{ id: 'screen-1', sections: [] }],
      };
      const transformedPayload = { ...testPayload };
      mockPayloadUtils.transformPayload.mockReturnValue(transformedPayload);
      mockPayloadUtils.generateId.mockReturnValue('generated-id');
      mockAssetModel.findOne
        .mockResolvedValueOnce(null) // No asset by id
        .mockResolvedValueOnce(null); // No asset by response_id
      const createdAsset = {
        ...transformedPayload,
        _id: 'generated-id',
        id: 'generated-id',
      };
      mockAssetModel.create.mockResolvedValue(createdAsset);
      
      const result = await service.createAsset([testPayload]);
      
      expect(mockPayloadUtils.generateId).toHaveBeenCalled();
      expect(mockAssetModel.create).toHaveBeenCalledWith(createdAsset);
      expect(result).toEqual([createdAsset]);
    });

    it('should handle server errors', async () => {
      const error = new Error('Something went wrong');
      mockAssetModel.create.mockRejectedValue(error);
      mockPayloadUtils.transformPayload.mockReturnValueOnce({ id: 'test-id' });
      const result = await service.createAsset([testPayload]);
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should handle database errors', async () => {
      mockPayloadUtils.transformPayload.mockImplementationOnce(() => {
        throw new Error('Something went wrong');
      });
      const result = await service.createAsset([payload]);
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should create asset when unique_entity_id is missing', async () => {
      const payloadWithMatchingRole = {
        ...testPayload,
        role: testPayload.entity[0],
        unique_entity_id: undefined
      };
      
      const transformedPayload = {
        ...payloadWithMatchingRole
      };

      mockPayloadUtils.transformPayload.mockReturnValueOnce(transformedPayload);
      mockPayloadUtils.generateId.mockReturnValueOnce('generated-id');
      mockAssetModel.findOne.mockResolvedValueOnce(null);
      const createdAsset = {
        ...transformedPayload,
        _id: 'generated-id',
        id: 'generated-id'
      };
      mockAssetModel.create.mockResolvedValueOnce(createdAsset);

      const result = await service.createAsset([payloadWithMatchingRole]);

      expect(mockAssetModel.create).toHaveBeenCalledWith({
        ...transformedPayload,
        _id: 'generated-id',
        id: 'generated-id'
      });
      expect(result).toEqual([createdAsset]);
    });

    it('should create asset with new ID when id is not in transformedPayload', async () => {
      const testPayload = {
        ...payload,
        entity: [payload.role], // Make sure entity is an array
        unique_entity_id: 'test-id',
        id: 'test-id' // Add id to test its removal in transformedPayload
      } as CreateAssetDto & { id?: string };

      const transformedPayload = { ...testPayload };
      delete transformedPayload.id; // Ensure no id in transformed payload
      
      mockPayloadUtils.transformPayload.mockReturnValueOnce(transformedPayload);
      mockAssetModel.findOne.mockResolvedValueOnce(null);
      mockPayloadUtils.generateId.mockReturnValueOnce('generated-id');
      
      const expectedAsset = {
        ...transformedPayload,
        _id: 'generated-id',
        id: 'generated-id'
      };
      mockAssetModel.create.mockResolvedValueOnce(expectedAsset);
      
      const result = await service.createAsset([testPayload]);
      
      expect(mockPayloadUtils.generateId).toHaveBeenCalled();
      expect(mockAssetModel.create).toHaveBeenCalledWith(expectedAsset);
      expect(result).toEqual([expectedAsset]);
    });

    it('should create an asset successfully', async () => {
      const asset = { ...payload, id: 'test-id' };
      mockAssetModel.findOne.mockResolvedValueOnce(asset);

      const result = await service.getAssetById('test-id');

      expect(mockAssetModel.findOne).toHaveBeenCalledWith({ 
        id: 'test-id', 
        delete_flag: 0 
      });
      expect(result).toEqual(asset);
    });

    it('should handle errors', async () => {
      const error = new Error('DB error');
      mockAssetModel.findOne.mockRejectedValue(error);
      
      const result = await service.getAssetById('test-id');
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('fetchAll', () => {
    it('should return all non-deleted assets', async () => {
      const mockAssets = [
        { id: 'asset-1', delete_flag: 0 },
        { id: 'asset-2', delete_flag: 0 }
      ];
      mockAssetModel.find.mockResolvedValue(mockAssets);

      const result = await service.fetchAll();

      expect(result).toEqual(mockAssets);
      expect(mockAssetModel.find).toHaveBeenCalledWith({ delete_flag: 0 });
    });

    it('should handle database error', async () => {
      const error = new Error('Database error');
      mockAssetModel.find.mockRejectedValue(error);
      mockErrorService.serverError.mockReturnValue(undefined);

      const result = await service.fetchAll();

      expect(result).toEqual([]);
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Database error');
      expect(mockAssetModel.find).toHaveBeenCalledWith({ delete_flag: 0 });
    });
  });

  describe('getAssetAssignment', () => {
    const mockUserId = 'user-1';
    const mockOrgId = 'org-1';
    const mockRoleId = 'role-1';
    const mockProgramId = 'program-1';
    const mockLastSyncTime = '2025-06-27T09:19:23.000Z';
    const mockPortfolio = {
      data: [{
        portfolio: [
          { id: 'asset-1' },
          { id: 'asset-2' }
        ]
      }]
    };
    const mockAssets = [
      { id: 'asset-1', organization_id: mockOrgId },
      { id: 'asset-2', organization_id: mockOrgId }
    ];

    beforeEach(() => {
      mockEntityPortfolioService.getStaffPortfolio = jest.fn();
    });

    it('should return assets for assigned portfolio', async () => {
      mockEntityPortfolioService.getStaffPortfolio.mockResolvedValue(mockPortfolio);
      mockAssetModel.find.mockResolvedValue(mockAssets);

      const result = await service.getAssetAssignment(
        mockUserId,
        mockOrgId,
        mockRoleId,
        mockProgramId,
        mockLastSyncTime
      );

      expect(result).toEqual({
        message: 'assets gotten successfully',
        status: 'success',
        data: mockAssets
      });
      expect(mockEntityPortfolioService.getStaffPortfolio).toHaveBeenCalledWith(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      );
      expect(mockAssetModel.find).toHaveBeenCalledWith({
        id: { $in: ['asset-1', 'asset-2'] },
        delete_flag: 0,
        organization_id: mockOrgId,
        updatedAt: { $gte: new Date(mockLastSyncTime) }
      });
    });

    it('should return empty array when no portfolio found', async () => {
      mockEntityPortfolioService.getStaffPortfolio.mockResolvedValue(null);

      const result = await service.getAssetAssignment(
        mockUserId,
        mockOrgId,
        mockRoleId,
        mockProgramId
      );

      expect(result).toEqual([]);
      expect(mockEntityPortfolioService.getStaffPortfolio).toHaveBeenCalledWith(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      );
    });

    it('should return empty array when no assets found', async () => {
      mockEntityPortfolioService.getStaffPortfolio.mockResolvedValue(mockPortfolio);
      mockAssetModel.find.mockResolvedValue([]);

      const result = await service.getAssetAssignment(
        mockUserId,
        mockOrgId,
        mockRoleId,
        mockProgramId,
        mockLastSyncTime
      );

      expect(result).toEqual({
        message: 'assets gotten successfully',
        status: 'success',
        data: []
      });
      expect(mockEntityPortfolioService.getStaffPortfolio).toHaveBeenCalledWith(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      );
      expect(mockAssetModel.find).toHaveBeenCalledWith({
        id: { $in: ['asset-1', 'asset-2'] },
        delete_flag: 0,
        organization_id: mockOrgId,
        updatedAt: { $gte: new Date(mockLastSyncTime) }
      });
    });
  });

  describe('createAsset', () => {
    const roleId = new Types.ObjectId();
    const mockPayload: CreateAssetDto & { id?: string } = {
      organization_id: new Types.ObjectId(),
      response_id: 'resp-1',
      unique_entity_id: 'entity-1',
      role: roleId,
      entity: [roleId], // Same as role for testing role-entity match
      program_ids: [new Types.ObjectId()],
      response: [{
        id: 'screen-1',
        sections: [{
          id: 'section-1',
          components: [{
            id: 'component-1',
            label: 'test',
            answer: 'test'
          }]
        }]
      }],
      id: 'asset-1' // Optional in CreateAssetDto but required in Asset schema
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should create a new asset when no matching record exists', async () => {
      const transformedPayload = { ...mockPayload, _id: new Types.ObjectId() };
      mockPayloadUtils.transformPayload.mockReturnValue(transformedPayload);
      mockAssetModel.findOne.mockResolvedValue(null);
      mockAssetModel.create.mockResolvedValue(transformedPayload);

      const result = await service.createAsset([mockPayload]);

    });

    it('should update existing asset by response_id', async () => {
      const mockPayload = {
        ...testPayload,
        response_id: 'test-response-id',
      };

      const transformedPayload = { ...mockPayload };
      const existingAsset = { ...mockPayload };
      const updatedAsset = { ...mockPayload };

      mockPayloadUtils.transformPayload.mockReturnValueOnce(transformedPayload);
      mockAssetModel.findOne
        .mockResolvedValueOnce(existingAsset); // Asset found by response_id
      mockAssetModel.findOneAndUpdate.mockResolvedValueOnce(updatedAsset);

      const result = await service.createAsset([mockPayload]);

      // Should check by response_id
      expect(mockAssetModel.findOne).toHaveBeenCalledWith({
        response_id: mockPayload.response_id,
        delete_flag: 0
      });

      expect(mockAssetModel.findOneAndUpdate).toHaveBeenCalledWith(
        { response_id: mockPayload.response_id, delete_flag: 0 },
        transformedPayload,
        { new: true, upsert: true, setDefaultsOnInsert: true }
      );
      expect(result).toEqual([updatedAsset]);
    });

    it('should update asset by unique_entity_id when role matches', async () => {
      const roleObjectId = new Types.ObjectId();
      const payload = {
        organization_id: new Types.ObjectId(),
        response_id: 'resp-1',
        unique_entity_id: 'unique-1',
        role: roleObjectId,
        entity: [roleObjectId], // Using same ObjectId for role match
        program_ids: [new Types.ObjectId()],
        response: [{ id: 'screen-1', sections: [] }],
      };
      const transformedPayload = { ...payload };
      const existingAsset = { ...transformedPayload };
      const updatedAsset = { ...transformedPayload };

      mockPayloadUtils.transformPayload.mockReturnValue(transformedPayload);
      mockAssetModel.findOne
        .mockResolvedValueOnce(null)  // First findOne for response_id check
        .mockResolvedValueOnce(existingAsset);  // Second findOne for unique_entity_id check
      mockAssetModel.findOneAndUpdate.mockResolvedValue(updatedAsset);

      const result = await service.createAsset([payload]);

      expect(result).toEqual([updatedAsset]);
      expect(mockAssetModel.findOne).toHaveBeenCalledWith(
        { response_id: payload.response_id, delete_flag: 0 }
      );
      expect(mockAssetModel.findOne).toHaveBeenCalledWith(
        { id: payload.unique_entity_id, delete_flag: 0 }
      );
      expect(mockAssetModel.findOneAndUpdate).toHaveBeenCalledWith(
        { id: payload.unique_entity_id, delete_flag: 0 },
        transformedPayload,
        { new: true, upsert: true, setDefaultsOnInsert: true }
      );
    });

    it('should generate new id when not provided in transformed payload', async () => {
      const payload: CreateAssetDto & { id?: string } = {
        organization_id: new Types.ObjectId(),
        response_id: 'resp-1',
        unique_entity_id: 'entity-1',
        role: new Types.ObjectId(),
        entity: [new Types.ObjectId()],
        program_ids: [new Types.ObjectId()],
        response: [{ id: 'screen-1', sections: [] }]
      };
      const generatedId = new Types.ObjectId().toString();
      const transformedPayload = { ...payload };
      const newAsset = { ...transformedPayload, _id: generatedId, id: generatedId };

      mockPayloadUtils.transformPayload.mockReturnValue(transformedPayload);
      mockPayloadUtils.generateId.mockReturnValue(generatedId);
      mockAssetModel.findOne.mockResolvedValue(null);
      mockAssetModel.create.mockResolvedValue(newAsset);

      const result = await service.createAsset([payload]);

      expect(result).toEqual([newAsset]);
      expect(mockAssetModel.create).toHaveBeenCalledWith({
        ...transformedPayload,
        _id: generatedId,
        id: generatedId
      });
    });

    it('should update asset by response_id when found', async () => {
      const transformedPayload = { ...mockPayload };
      delete transformedPayload.id; // Ensure no id to force response_id path
      const existingAsset = { ...mockPayload };
      const updatedAsset = { ...existingAsset, updatedAt: new Date() };

      mockPayloadUtils.transformPayload.mockReturnValueOnce(transformedPayload);
      mockAssetModel.findOne
        .mockResolvedValueOnce(existingAsset); // Asset found by response_id
      mockAssetModel.findOneAndUpdate.mockResolvedValueOnce(updatedAsset);

      const result = await service.createAsset([mockPayload]);

      expect(mockAssetModel.findOne).toHaveBeenCalledWith({
        response_id: mockPayload.response_id,
        delete_flag: 0
      });
      expect(mockAssetModel.findOneAndUpdate).toHaveBeenCalledWith(
        { response_id: mockPayload.response_id, delete_flag: 0 },
        transformedPayload,
        { new: true, upsert: true, setDefaultsOnInsert: true }
      );
      expect(result).toEqual([updatedAsset]);
    });

    it('should create new asset when role matches entity and has unique_entity_id', async () => {
      const transformedPayload = { ...mockPayload };
      delete transformedPayload.id;
      const newId = 'new-generated-id';
      const newAsset = { ...transformedPayload, _id: newId, id: newId };

      mockPayloadUtils.transformPayload.mockReturnValueOnce(transformedPayload);
      mockPayloadUtils.generateId.mockReturnValueOnce(newId);
      mockAssetModel.findOne
        .mockResolvedValueOnce(null) // No asset by id
        .mockResolvedValueOnce(null) // No asset by response_id
        .mockResolvedValueOnce(null); // No asset by unique_entity_id
      mockAssetModel.create.mockResolvedValueOnce(newAsset);

      const result = await service.createAsset([mockPayload]);

      expect(mockPayloadUtils.generateId).toHaveBeenCalled();
      expect(mockAssetModel.create).toHaveBeenCalledWith(newAsset);
      expect(result).toEqual([newAsset]);
    });

    it('should handle transform payload errors', async () => {
      mockPayloadUtils.transformPayload.mockImplementation(() => {
        throw new Error('Transform error');
      });

      const result = await service.createAsset([mockPayload]);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should handle database errors during create', async () => {
      const transformedPayload = { ...mockPayload };
      mockPayloadUtils.transformPayload.mockReturnValue(transformedPayload);
      mockAssetModel.findOne.mockResolvedValue(null);
      mockAssetModel.create.mockRejectedValue(new Error('Database error'));
      expect.assertions(2);

      const result = await service.createAsset([mockPayload]);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should handle database errors during findOne', async () => {
      const transformedPayload = { ...mockPayload };
      mockPayloadUtils.transformPayload.mockReturnValue(transformedPayload);
      mockAssetModel.findOne.mockRejectedValue(new Error('Find error'));

      const result = await service.createAsset([mockPayload]);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should handle database errors during findOneAndUpdate', async () => {
      const transformedPayload = { ...mockPayload };
      mockPayloadUtils.transformPayload.mockReturnValue(transformedPayload);
      mockAssetModel.findOne.mockResolvedValue({ ...mockPayload });
      mockAssetModel.findOneAndUpdate.mockRejectedValue(new Error('Update error'));

      const result = await service.createAsset([mockPayload]);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('getAllAssets', () => {
    const mockOrgId = new Types.ObjectId().toString();

    it('should return all assets for organization with last_sync_time', async () => {
      const assets = [payload];
      const lastSyncTime = '2025-06-27T14:57:59+01:00';
      mockAssetModel.find.mockResolvedValueOnce(assets);

      const result = await service.getAllAssets(mockOrgId, lastSyncTime);

      expect(mockAssetModel.find).toHaveBeenCalledWith({ 
        organization_id: mockOrgId, 
        delete_flag: 0,
        updatedAt: { $gte: new Date(lastSyncTime) }
      });
      expect(result).toEqual(assets);
    });

    it('should handle invalid date format in last_sync_time', async () => {
      const invalidDate = 'invalid-date';
      const error = new Error('Invalid date format');
      
      // Mock find to throw error when invalid date is passed
      mockAssetModel.find.mockRejectedValueOnce(error);
      mockErrorService.serverError.mockReturnValueOnce(undefined);

      const result = await service.getAllAssets(mockOrgId, invalidDate);

      expect(result).toBeUndefined(); // Service returns undefined on error, not empty array
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
      // Don't check mockAssetModel.find args since the date parsing will fail
    });

    it('should handle database errors', async () => {
      const error = new Error('Database error');
      mockAssetModel.find.mockRejectedValueOnce(error);
      
      const result = await service.getAllAssets(mockOrgId);
      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return assets for organization with last_sync_time', async () => {
      const mockAssets = [{ id: 'asset-1' }];
      const lastSyncTime = '2025-06-27T12:00:00Z';
      mockAssetModel.find.mockResolvedValue(mockAssets);

      const result = await service.getAllAssets(mockOrgId, lastSyncTime);

      expect(result).toEqual(mockAssets);
      expect(mockAssetModel.find).toHaveBeenCalledWith({
        organization_id: mockOrgId,
        delete_flag: 0,
        updatedAt: { $gte: expect.any(Date) }
      });
    });

    it('should handle database errors', async () => {
      const error = new Error('Database error');
      mockAssetModel.find.mockRejectedValue(error);

      const result = await service.getAllAssets(mockOrgId);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should handle invalid date format in last_sync_time', async () => {
      const invalidDate = 'invalid-date';
      mockErrorService.serverError.mockReturnValue(undefined);
      // Mock getLastSyncTime to throw an error
      jest.spyOn(require('../utils/last_sync.utils'), 'getLastSyncTime').mockImplementation(() => {
        throw new Error('Invalid date format');
      });

      const result = await service.getAllAssets(mockOrgId, invalidDate);

      expect(result).toBeUndefined(); // Service returns undefined on error
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('getProgramAssets', () => {
    it('should return assets for program and organization', async () => {
      const programId = new Types.ObjectId().toString();
      const orgId = new Types.ObjectId().toString();
      const mockAssets = [{ ...payload }];

      mockAssetModel.find.mockResolvedValueOnce(mockAssets);

      const result = await service.getProgramAssets(programId, orgId);

      expect(mockAssetModel.find).toHaveBeenCalledWith({
        program_ids: { $in: [new Types.ObjectId(programId)] },
        organization_id: new Types.ObjectId(orgId),
        delete_flag: 0
      });
      expect(result).toEqual(mockAssets);
    });

    it('should handle invalid ObjectId error', async () => {
      const invalidId = 'invalid-id';
      const orgId = new Types.ObjectId().toString();
      
      const result = await service.getProgramAssets(invalidId, orgId);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should handle database errors', async () => {
      const programId = new Types.ObjectId().toString();
      const orgId = new Types.ObjectId().toString();
      const error = new Error('Database error');
      
      mockAssetModel.find.mockRejectedValueOnce(error);
      
      const result = await service.getProgramAssets(programId, orgId);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    const mockProgramId = new Types.ObjectId().toString();
    const mockOrgId = new Types.ObjectId().toString();

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return assets for program and organization', async () => {
      const mockAssets = [{ id: 'asset-1' }];
      mockAssetModel.find.mockResolvedValue(mockAssets);

      const result = await service.getProgramAssets(mockProgramId, mockOrgId);

      expect(result).toEqual(mockAssets);
      expect(mockAssetModel.find).toHaveBeenCalledWith({
        program_ids: { $in: [expect.any(Types.ObjectId)] },
        organization_id: expect.any(Types.ObjectId),
        delete_flag: 0
      });
    });

    it('should handle database errors', async () => {
      const error = new Error('Database error');
      mockAssetModel.find.mockRejectedValue(error);

      const result = await service.getProgramAssets(mockProgramId, mockOrgId);

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });

    it('should handle invalid ObjectId errors', async () => {
      const result = await service.getProgramAssets('invalid-id', 'invalid-id');

      expect(result).toBeUndefined();
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Something went wrong');
    });
  });

  describe('fetchAll', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return all non-deleted assets', async () => {
      const mockAssets = [
        { id: 'asset-1', delete_flag: 0 },
        { id: 'asset-2', delete_flag: 0 }
      ];
      mockAssetModel.find.mockResolvedValue(mockAssets);

      const result = await service.fetchAll();

      expect(result).toEqual(mockAssets);
      expect(mockAssetModel.find).toHaveBeenCalledWith({ delete_flag: 0 });
    });

    it('should return empty array on error', async () => {
      const error = new Error('Database error');
      mockAssetModel.find.mockRejectedValue(error);

      const result = await service.fetchAll();

      expect(result).toEqual([]);
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Database error');
    });

    it('should handle empty result set', async () => {
      mockAssetModel.find.mockResolvedValue([]);

      const result = await service.fetchAll();

      expect(result).toEqual([]);
      expect(mockAssetModel.find).toHaveBeenCalledWith({ delete_flag: 0 });
    });
  });
});
