import { <PERSON><PERSON><PERSON><PERSON>, IsMongoId, IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { Type } from 'class-transformer';
import { EntityType } from '../enum/entity-assignment.enum';


export class CreateEntityAssignmentDto {
  @ApiProperty({
    description: 'Organization ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsMongoId()
  organization_id: Types.ObjectId;

  @ApiProperty({
    description: 'Assigned role ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsString()
  assigned_role_id: string;

  @ApiProperty({
    description: 'Assigned ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsOptional()
  @IsString()
  assigned_id: string;

  @ApiProperty({
    description: 'Assigned to ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsString()
  assigned_to_id: string;

  @ApiProperty({
    description: 'Assigned to role ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsString()
  assigned_to_role_id: string;

  @ApiProperty({
    description: 'Assigned to entity type',
    example: 'user'
  })
  @IsNotEmpty()
  @IsString()
  assigned_to_entity_type: EntityType;

  @ApiProperty({
    description: 'Program ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsMongoId()
  program_id: Types.ObjectId;
}