// validate-array.pipe.ts
import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';

@Injectable()
export class ValidateArrayPipe implements PipeTransform {
  constructor(private readonly classType: any) {}

  async transform(value: any, metadata: ArgumentMetadata) {
    if (!Array.isArray(value)) {
      throw new BadRequestException('Expected an array');
    }

    const objects:any = plainToInstance(this.classType, value);

    for (const obj of objects) {
      const errors = await validate(obj, { whitelist: true, forbidNonWhitelisted: true });
      if (errors.length > 0) {
        throw new BadRequestException(errors);
      }
    }

    return objects;
  }
}
