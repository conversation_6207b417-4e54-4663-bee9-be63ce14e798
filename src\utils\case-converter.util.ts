import { Injectable } from "@nestjs/common";

@Injectable()
export class CaseConverterUtil {



 camelToSnakeCase(str: string): string {
    // <PERSON>le first character separately to avoid leading underscore
    const result = str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    // Convert first character to lowercase without adding underscore
    return result.charAt(0).toLowerCase() + result.slice(1);
  }

 convertKeysToSnakeCase(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map(v => this.convertKeysToSnakeCase(v));
    } else if (obj !== null && obj.constructor === Object) {
      return Object.keys(obj).reduce(
        (result, key) => {
          // Skip keys that are already in snake_case
          const newKey = key.includes('_') ? key : this.camelToSnakeCase(key);
          return {
            ...result,
            [newKey]: this.convertKeysToSnakeCase(obj[key])
          };
        },
        {},
      );
    }
    return obj;
}
}