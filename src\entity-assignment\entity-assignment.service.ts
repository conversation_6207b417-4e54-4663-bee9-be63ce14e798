import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { EntityAssignment, EntityAssignmentDocument } from './schema/entity-assignment.schema';
import { ErrorService } from '../error/error.service';
import { CreateEntityAssignmentDto } from './dto/entity-assignment.dto';
import { EntityPortfolio, EntityPorfolioDocument } from '../entity-portfolio/schema/entity-portfolo.schema';
import { EntityPortfolioService } from '../entity-portfolio/entity-portfolio.service';
import { getLastSyncTime } from '../utils/last_sync.utils';

@Injectable()
export class EntityAssignmentService {
    constructor(
        @InjectModel(EntityAssignment.name)
        private readonly entityAssignmentModel: Model<EntityAssignmentDocument>,
        @InjectModel(EntityPortfolio.name)
        private readonly entityPortfolioModel: Model<EntityPorfolioDocument>,
        private readonly errorService: ErrorService,
        private readonly entityPortfolioService: EntityPortfolioService
    ) {}

    async createEntityAssignment(payload: CreateEntityAssignmentDto): Promise<any> {
        try {
            const result = await this.entityAssignmentModel.create(payload);
            return {message: 'Entity assignment created successfully', status: 'success', data: result};
        } catch (error) {
            this.errorService.serverError(error.message);
        }
    }

    async getStaffAssignment(user_id: string, role_id: string, tenant_id: string, program_id: string): Promise<any> {
        try {
            const result = await this.entityAssignmentModel.findOne({
                user_id,
                role_id,
                tenant_id,
                program_id
            });
            return {
                message: 'Staff assignment retrieved successfully',
                status: 'success',
                data: result
            };
        } catch (error) {
            this.errorService.serverError(error.message);
        }
    }


  /**
   * Gets the assignments in that program
   * @param program_id - The ID of the program
   * @returns The assignments in that program
   */
  async getAssignmentsByProgram(
    program_id: string,
    last_sync_time?: string
  ) {
    try {
      const last_sync= getLastSyncTime(last_sync_time);
      const assignments = await this.entityAssignmentModel.find({
        program_id,
        updatedAt: { $gte: last_sync },
      });
      return {
        message: 'Assignments gotten successfully',
        status: 'success',
        data: assignments,
      };
    } catch (error: any) {
      this.errorService.serverError(error.message);
      return undefined;
    }
  }

}
