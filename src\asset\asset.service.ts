import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Asset, AssetDocument } from './schema/asset.schema';
import { CreateAssetDto } from './dto/asset.dto';
import { ErrorService } from '../error/error.service';
import { PayloadUtils } from '../utils/payload.utils';
import { EntityPortfolioService } from '../entity-portfolio/entity-portfolio.service';
import { getLastSyncTime } from '../utils/last_sync.utils';

@Injectable()
export class AssetService {
  constructor(
    @InjectModel(Asset.name)
    private readonly assetModel: Model<AssetDocument>,
    private readonly errorService: ErrorService,
    private readonly payloadUtils: PayloadUtils,
    private readonly entityPortfolioService: EntityPortfolioService,
  ) {}

  /**
   * Creates or updates assets based on the payload array
   * @param payloads - Array of payloads containing the details of the assets
   * @returns Array of created or updated assets
   */
  async createAsset(
    payloads: CreateAssetDto[],
  ): Promise<AssetDocument[] | undefined> {
    try {
      const results: AssetDocument[] = [];

      for (const payload of payloads) {
        const transformedPayload = this.payloadUtils.transformPayload(payload);

        // If id exists create or update based off the id
        if ('id' in transformedPayload && transformedPayload.id) {
          const data = await this.assetModel.findOne({
            id: transformedPayload.id,
            delete_flag: 0,
          });

          if (data) {
            const result = await this.assetModel.findOneAndUpdate(
              { id: transformedPayload.id, delete_flag: 0 },
              transformedPayload,
              { new: true, upsert: true, setDefaultsOnInsert: true },
            );
            results.push(result);
            continue;
          }

          const newAsset = await this.assetModel.create(transformedPayload);
          results.push(newAsset);
          continue;
        }

        //if response_id exists update the existing record
        if(payload.response_id){
          const data = await this.assetModel.findOne({
            response_id: payload.response_id,
            delete_flag: 0,
          });
          if (data) {
            const result = await this.assetModel.findOneAndUpdate(
              { response_id: payload.response_id, delete_flag: 0 },
              transformedPayload,
              { new: true, upsert: true, setDefaultsOnInsert: true },
            );
            results.push(result);
            continue;
          }
        }
        // If the role of the person carrying out the activity matches the role of the target asset
        // if (payload.role === payload.entity[0]) {
          if (payload.unique_entity_id && payload.entity[0] == payload.role) {
            const data = await this.assetModel.findOne({
              id: payload.unique_entity_id,
              delete_flag: 0,
            });

            if (data) {
              const result = await this.assetModel.findOneAndUpdate(
                { id: payload.unique_entity_id, delete_flag: 0 },
                transformedPayload,
                { new: true, upsert: true, setDefaultsOnInsert: true },
              );
              results.push(result);
              continue;
            }

            if (!('id' in transformedPayload)) {
              const newId = this.payloadUtils.generateId();
              const newAsset = await this.assetModel.create({
                ...transformedPayload,
                _id: newId,
                id: newId,
              });
              results.push(newAsset);
              continue;
            }


            const newAsset = await this.assetModel.create(transformedPayload);
            results.push(newAsset);
            continue;
          } else {
            if (!('id' in transformedPayload)) {
              const newId = this.payloadUtils.generateId();
              const newAsset = await this.assetModel.create({
                ...transformedPayload,
                _id: newId,
                id: newId,
              });
              results.push(newAsset);
              continue;
            }
          }
        // } else {
        //   if (!('id' in transformedPayload)) {
        //     const newId = this.payloadUtils.generateId();
        //     const newAsset = await this.assetModel.create({
        //       ...transformedPayload,
        //       _id: newId,
        //       id: newId,
        //     });
        //     results.push(newAsset);
        //     continue;
        //   }
        //   const newAsset = await this.assetModel.create(transformedPayload);
        //   results.push(newAsset);
        //   continue;
        // }
      }
      return results;
    } catch (error: any) {
      console.log(error);
      this.errorService.serverError('Something went wrong');
      return undefined;
    }
  }

  async getAssetById(id: string) {
    try {
      return await this.assetModel.findOne({ id, delete_flag: 0 });
    } catch (error: any) {
      this.errorService.serverError('Something went wrong');
      return undefined;
    }
  }

  async getAllAssets(organizationId: string, last_sync_time?: string) {
    try {
      const last_sync= getLastSyncTime(last_sync_time);
      return await this.assetModel.find({
        organization_id: organizationId,
        delete_flag: 0,
        updatedAt: { $gte: last_sync },
      });
    } catch (error: any) {
      this.errorService.serverError('Something went wrong');
      return undefined;
    }
  }
  async getAssetAssignment(
    user_id: string,
    organization_id: string,
    role_id: string,
    program_id: string,
    last_sync_time?: string,
  ) {
    try {
      const last_sync= getLastSyncTime(last_sync_time);
      const assignment_data = await this.entityPortfolioService.getStaffPortfolio(user_id,role_id,organization_id,program_id);
      if (!assignment_data) {
        return [];
      }

      const portfolio = assignment_data.data[0].portfolio;

      const ids = portfolio.map((item:any) => item.id);

      const assets = await this.assetModel.find({
        id: { $in: ids },
        delete_flag: 0,
        organization_id,
        updatedAt: { $gte: last_sync },
      });
      return {
        message: 'assets gotten successfully',
        status: 'success',
        data: assets,
      };
    } catch (error: any) {
      this.errorService.serverError(error.message);
      return undefined;
    }
  }
  async fetchAll() {
    try {
      const data = await this.assetModel.find({ delete_flag: 0 });
      return data;
    } catch (err) {
      console.log(err.message);
      this.errorService.serverError(err.message);
      return [];
    }
  }

  async getProgramAssets(program_id: string, organization_id: string) {
      try {
        const assets = await this.assetModel.find({
          program_ids: { $in: [new Types.ObjectId(program_id)] },
          organization_id: new Types.ObjectId(organization_id),
          delete_flag: 0,
        });
        
        return assets;
      } catch (error: any) {
        console.log(error);
        this.errorService.serverError('Something went wrong');
        return undefined;
      }
    } 
}
