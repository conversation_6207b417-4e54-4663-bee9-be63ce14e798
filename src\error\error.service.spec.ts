import { Test, TestingModule } from '@nestjs/testing';
import { ErrorService } from './error.service';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('ErrorService', () => {
  let service: ErrorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ErrorService],
    }).compile();

    service = module.get<ErrorService>(ErrorService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('serverError', () => {
    it('should throw HttpException with INTERNAL_SERVER_ERROR status', () => {
      const errorMessage = 'Server error occurred';
      
      try {
        service.serverError(errorMessage);
        fail('Expected serverError to throw');
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
        expect(error.getResponse()).toEqual({
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: errorMessage
        });
      }
    });
  });

  describe('badRequestError', () => {
    it('should throw HttpException with BAD_REQUEST status', () => {
      const errorMessage = 'Bad request error';
      
      try {
        service.badRequestError(errorMessage);
        fail('Expected badRequestError to throw');
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.getStatus()).toBe(HttpStatus.BAD_REQUEST);
        expect(error.getResponse()).toEqual({
          status: HttpStatus.BAD_REQUEST,
          error: errorMessage
        });
      }
    });
  });

  describe('notFoundError', () => {
    it('should throw HttpException with NOT_FOUND status', () => {
      const errorMessage = 'Resource not found';
      
      try {
        service.notFoundError(errorMessage);
        fail('Expected notFoundError to throw');
      } catch (error) {
        expect(error).toBeInstanceOf(HttpException);
        expect(error.getStatus()).toBe(HttpStatus.NOT_FOUND);
        expect(error.getResponse()).toEqual({
          status: HttpStatus.NOT_FOUND,
          error: errorMessage
        });
      }
    });
  });
});
