import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { any } from 'joi';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type EntityPorfolioDocument = EntityPortfolio &
  Document & {
    [key: string]: any;
  };

export class PortfolioItem {
  @Prop({ required: true, type: String })
  id: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true })
  role_id: MongooseSchema.Types.ObjectId;
}

@Schema({
  toJSON: {
    virtuals: false,
    getters: true,
  },
  toObject: {
    virtuals: false,
    getters: true,
  },
  timestamps: true,
  strict: true,
})
export class EntityPortfolio {
  @Prop({ required: true, type: String })
  id: string;

  @Prop({ required: true, type: [PortfolioItem] })
  portfolio: string;

  @Prop({ required: true, type: MongooseSchema.Types.ObjectId })
  organization_id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, type: MongooseSchema.Types.ObjectId })
  program_id: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, type: MongooseSchema.Types.ObjectId })
  role_id: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, type: Number, default: 1 })
  is_valid: number;
}

export const EntityPortfolioSchema =
  SchemaFactory.createForClass(EntityPortfolio);

// Add compound unique index on id and role_id
EntityPortfolioSchema.index({ id: 1, role_id: 1 }, { unique: true });

EntityPortfolioSchema.methods.toJSON = function () {
  const obj = this.toObject();
  obj.created_at = obj.createdAt || null;
  obj.updated_at = obj.updatedAt || null;

  delete obj.updatedAt;
  delete obj.createdAt;
  return obj;
};
