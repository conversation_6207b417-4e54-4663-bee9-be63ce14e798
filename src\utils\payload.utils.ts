import { Injectable } from '@nestjs/common';
import { Types } from "mongoose";

@Injectable()
export class PayloadUtils {
  transformPayload(payload: any) {
    const { response,entity, ...rest } = payload;
    delete rest.unique_entity_id
    delete rest.role
    const mergedResult = response.flatMap(screen =>
      screen.sections.flatMap(section =>
        section.components.map(comp => {
          const identifier = comp.tag || comp.label;
          const snakeTag = identifier
            .toLowerCase()
            .replace(/[^\w\s]/g, "")
            .replace(/\n.*$/, "") 
            .replace(/\s+/g, "_");   
          return { [snakeTag]: comp.answer };
        })
      )
    ).reduce((acc, curr) => ({ ...acc, ...curr }), {});
    return { 
      ...rest,
      ...mergedResult,
      role_ids: [entity[0]]
    };
  }
  
  generateId(): string {
    return new Types.ObjectId().toString();
  }
}