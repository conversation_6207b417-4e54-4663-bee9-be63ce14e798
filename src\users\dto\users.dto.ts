import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>E<PERSON>,
  IsMongoId,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { Type } from 'class-transformer';
import { ComparisonOperator } from '../enum/user.enum';

export class ComponentDto {
  @ApiProperty({
    description: 'Component ID',
    example: 'single_choice_747081',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Component label',
    example: 'Select Role',
  })
  @IsString()
  @IsOptional()
  label?: string;

  @ApiProperty({
    description: 'Component tag',
    example: 'select_role',
  })
  @IsString()
  @IsOptional()
  tag?: string;

  @ApiProperty({
    description: 'Component answer',
    example: 'Prospective Entrepreneur',
  })
  @IsString()
  @IsNotEmpty()
  answer: string;
}

export class SectionDto {
  @ApiProperty({
    description: 'Section ID',
    example: 'section_479098',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Section components',
    type: [ComponentDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ComponentDto)
  components: ComponentDto[];
}

export class ScreenDto {
  @ApiProperty({
    description: 'Screen ID',
    example: 'screen_131160',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Screen sections',
    type: [SectionDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SectionDto)
  sections: SectionDto[];
}

export class CreateUserDto {
  @ApiProperty({
    description: 'Organization ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsNotEmpty()
  @IsMongoId()
  organization_id: Types.ObjectId;

  @ApiProperty({
    description: 'Badges array',
    example: ['entrepreneur'],
  })
  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true })
  badge_ids?: Types.ObjectId[];

  @ApiProperty({
    description: 'Entity array',
    example: ['entrepreneur'],
  })
  @IsArray()
  @IsMongoId({ each: true })
  @IsNotEmpty()
  entity: Types.ObjectId[];

  @ApiProperty({
    description: 'Role',
    example: '507f1f77bcf86cd799439011',
  })
  @IsMongoId()
  role: Types.ObjectId;

  @ApiProperty({
    description: 'Programs array',
    example: ['507f1f77bcf86cd799439011'],
  })
  @IsArray()
  @IsMongoId({ each: true })
  program_ids: Types.ObjectId[];

  @ApiProperty({
    description: 'Unique Entity ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsOptional()
  @IsString()
  unique_entity_id: string;

  @ApiProperty({
    description: 'Facial Template',
    example: 'facial template',
  })
  @IsString()
  @IsOptional()
  facial_template?: string;

  @ApiProperty({
    description: 'Response array containing screens',
    type: [ScreenDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ScreenDto)
  @IsNotEmpty()
  response: ScreenDto[];

  @ApiProperty({
    description: 'Response ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsNotEmpty()
  @IsString()
  response_id: string;

  @ApiProperty({
    description: 'Target ID',
    example: '507f1f77bcf86cd799439011',
  })
  target_id:string 

  @ApiProperty({
    description: 'Performer ID',
    example: '507f1f77bcf86cd799439011',
  })
  performer_id: string

  @ApiProperty({
    description: 'Home Office',
    example: {
      id: "683db01d6d0c08456b43be4d",
      code: "FAHUB-NG-0101001",
      name: "Hub Alpha",
      level: "hub"},
  })
  home_office: object
}

export class RequestParticipantsDTO {
  @ApiProperty({
    description: 'Program ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsNotEmpty()
  @IsDefined({ message: 'program_id is required' })
  @IsMongoId()
  program_id: Types.ObjectId;

  @ApiProperty({
    description: 'Role ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsNotEmpty()
  @IsMongoId()
  entity_role_id: Types.ObjectId;

  @ApiProperty({
    description: 'Organization ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsNotEmpty()
  @IsMongoId()
  organization_id: Types.ObjectId;

  @IsOptional()
  entity_ids: string[];
}

export class RequestRegisteredDTO {
  @ApiProperty({
    description: 'Onboarding Date',
    example: '2025-04-09',
  })
  @IsNotEmpty()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'date must be in the format YYYY-MM-DD',
  })
  onboarding_date: string;

  @ApiProperty({
    description: 'Comparision Operator',
    example: 'earlier_than',
  })
  @IsNotEmpty()
  @IsEnum(ComparisonOperator, {
    message: 'comparison_operator must be one of: earlier_than, later_than',
  })
  comparison_operator: ComparisonOperator;

  @ApiProperty({
    description: 'Organization ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsNotEmpty()
  @IsMongoId()
  organization_id: Types.ObjectId;

  @IsOptional()
  entity_ids: string[];
}