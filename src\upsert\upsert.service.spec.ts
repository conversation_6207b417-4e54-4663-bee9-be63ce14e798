import { Test, TestingModule } from '@nestjs/testing';
import { UpsertService } from './upsert.service';
import { ErrorService } from '../error/error.service';
import { UsersService } from '../users/users.service';
import { getConnectionToken } from '@nestjs/mongoose';
import { Collection, Connection } from 'mongoose';

describe('UpsertService', () => {
  let service: UpsertService;
  let mockErrorService: jest.Mocked<ErrorService>;
  let mockUserService: jest.Mocked<UsersService>;
  let mockConnection: jest.Mocked<Connection>;
  let mockCollection: jest.Mocked<Collection>;

  describe('tryConvertToObjectId', () => {
    it('should handle non-object values', () => {
      const input = 'simple string';
      const result = (service as any).tryConvertToObjectId(input);
      expect(result).toBe(input);
    });

    it('should handle ObjectId parsing errors', () => {
      const input = { _id: { $oid: {} } }; // Invalid ObjectId that will cause error
      const result = (service as any).tryConvertToObjectId(input);
      expect(result).toEqual(input);
    });

    it('should handle Date parsing errors', () => {
      const input = { date: { $date: undefined } }; // undefined will cause Date constructor to throw
      const result = (service as any).tryConvertToObjectId(input);
      expect(result).toEqual(input);
    });
    it('should handle invalid ObjectId in extended JSON format', () => {
      const input = { _id: { $oid: 'invalid' } };
      const result = (service as any).tryConvertToObjectId(input);
      expect(result).toEqual(input);
    });

    it('should handle invalid Date in extended JSON format', () => {
      const input = { date: { $date: 'invalid' } };
      const result = (service as any).tryConvertToObjectId(input);
      expect(result.date instanceof Date).toBe(true);
      expect(result.date.toString()).toBe('Invalid Date');
    });

    it('should handle invalid string ObjectId', () => {
      const input = { _id: '123' }; // Not a valid 24-char hex
      const result = (service as any).tryConvertToObjectId(input);
      expect(result).toEqual(input);
    });

    it('should handle nested objects and arrays', () => {
      const input = {
        nested: { _id: { $oid: 'invalid' } },
        array: [{ $oid: 'invalid' }, { nested: { $oid: 'invalid' } }]
      };
      const result = (service as any).tryConvertToObjectId(input);
      expect(result).toEqual(input);
    });
  });

  beforeEach(async () => {
    mockErrorService = {
      serverError: jest.fn(),
      badRequestError: jest.fn(),
    } as any;

    mockUserService = {
      createEvent: jest.fn(),
    } as any;

    mockCollection = {
      bulkWrite: jest.fn(),
      findOne: jest.fn(),
    } as any;

    mockConnection = {
      collection: jest.fn().mockReturnValue(mockCollection),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UpsertService,
        {
          provide: ErrorService,
          useValue: mockErrorService,
        },
        {
          provide: getConnectionToken(),
          useValue: mockConnection,
        },
        {
          provide: UsersService,
          useValue: mockUserService,
        },
      ],
    }).compile();

    service = module.get<UpsertService>(UpsertService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('upsert', () => {
    it('should handle update with program_ids length change', async () => {
      const resource = 'users';
      const operation = 'update';
      const data = [{
        id: '123',
        name: 'test',
        program_ids: ['p1', 'p2'],
        role_ids: ['r1', 'r2'],
        organization_id: 'org1'
      }];
      const where = [{ id: '123' }];

      // Mock existing user with different program_ids length
      mockCollection.findOne.mockResolvedValueOnce({ 
        id: '123',
        program_ids: ['old_p1']
      });

      mockCollection.bulkWrite.mockResolvedValue({
        ok: 1,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 0,
        upsertedIds: {}
      } as any);

      await service.upsert(resource, operation, data, where);

      // Verify that createEvent was called with the correct events
      expect(mockUserService.createEvent).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            organization_id: 'org1',
            entity_id: '123',
            event_type: 'program_participation',
            program_participated_id: expect.any(String)
          })
        ])
      );
    });
    it('should handle insert with program_ids length mismatch', async () => {
      const resource = 'users';
      const operation = 'insert';
      const data = [{
        id: '123',
        name: 'test',
        program_ids: ['p1', 'p2'],
        role_ids: ['r1', 'r2'],
        organization_id: 'org1'
      }];

      mockCollection.findOne.mockResolvedValue({ program_ids: ['old_p1'] });
      mockCollection.bulkWrite.mockResolvedValue({
        ok: 1,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 0,
        upsertedIds: {}
      } as any);

      const result = await service.upsert(resource, operation, data);

      expect(mockUserService.createEvent).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            event_type: 'program_participation',
            program_id: 'p1'
          })
        ])
      );
      expect(result.modifiedCount).toBe(1);
    });
    it('should successfully update a document', async () => {
      const resource = 'users';
      const operation = 'update';
      const data = [{ name: 'test' }];
      const where = [{ id: '123' }];

      mockCollection.bulkWrite.mockResolvedValue({
        ok: 1,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 0,
        upsertedIds: {}
      } as any);

      const result = await service.upsert(resource, operation, data, where);
      
      expect(mockConnection.collection).toHaveBeenCalledWith(resource);
      expect(mockCollection.bulkWrite).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            updateOne: {
              filter: where[0],
              update: { $set: data[0] },
              upsert: true
            }
          })
        ])
      );
      expect(result).toEqual({
        ok: true,
        matchedCount: 1,
        modifiedCount: 1,
        upsertedCount: 0,
        upsertedIds: {},
        status: 'success',
        message: 'users update successfully'
      });
    });

    it('should successfully insert a document', async () => {
      const resource = 'users';
      const operation = 'insert';
      const data = [{ id: '123', name: 'test', program_ids: ['p1'], role_ids: ['r1'], organization_id: 'org1' }];

      mockCollection.findOne.mockResolvedValue(null);
      mockCollection.bulkWrite.mockResolvedValue({
        ok: 1,
        matchedCount: 0,
        modifiedCount: 0,
        upsertedCount: 1,
        upsertedIds: { '0': '123' }
      } as any);

      const result = await service.upsert(resource, operation, data);

      expect(mockConnection.collection).toHaveBeenCalledWith(resource);
      expect(mockCollection.bulkWrite).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            updateOne: {
              filter: { id: '123' },
              update: expect.any(Object),
              upsert: true
            }
          })
        ])
      );
      expect(mockUserService.createEvent).toHaveBeenCalled();
      expect(result).toEqual({
        ok: true,
        matchedCount: 0,
        modifiedCount: 0,
        upsertedCount: 1,
        upsertedIds: { '0': '123' },
        status: 'success',
        message: 'users insert successfully'
      });
    });

    it('should handle bulk operations with multiple items', async () => {
      const resource = 'users';
      const operation = 'update';
      const data = [
        { name: 'test1', program_ids: ['p1'] },
        { name: 'test2', program_ids: ['p2'] }
      ];
      const where = [{ id: '123' }, { id: '456' }];

      mockCollection.findOne.mockResolvedValue({ program_ids: [] });
      mockCollection.bulkWrite.mockResolvedValue({
        ok: 1,
        matchedCount: 2,
        modifiedCount: 2,
        upsertedCount: 0,
        upsertedIds: {}
      } as any);

      const result = await service.upsert(resource, operation, data, where);

      expect(mockCollection.bulkWrite).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            updateOne: {
              filter: where[0],
              update: { $set: data[0] },
              upsert: true
            }
          }),
          expect.objectContaining({
            updateOne: {
              filter: where[1],
              update: { $set: data[1] },
              upsert: true
            }
          })
        ])
      );
      expect(result.modifiedCount).toBe(2);
    });

    it('should handle invalid input - non-array data', async () => {
      const result = await service.upsert('users', 'update', 'invalid' as any);
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Data must be an array');
      expect(result).toBeUndefined();
    });

    it('should handle invalid input - mismatched where/data lengths', async () => {
      const data = [{ name: 'test1' }, { name: 'test2' }];
      const where = [{ id: '123' }];
      const result = await service.upsert('users', 'update', data, where);
      expect(mockErrorService.serverError).toHaveBeenCalledWith(
        'Both data and where must be arrays of the same length for update operation'
      );
      expect(result).toBeUndefined();
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('Test error');
      mockCollection.bulkWrite.mockRejectedValue(error);

      const result = await service.upsert('users', 'update', [{ name: 'test' }], [{ id: '123' }]);
      expect(mockErrorService.serverError).toHaveBeenCalledWith('Test error');
      expect(result).toBeUndefined();
    });
  });
});
