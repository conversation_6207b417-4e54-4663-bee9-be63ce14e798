import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type AssetDocument = Asset & Document & {
  [key: string]: any;
};

@Schema({
  toJSON: {
    virtuals: false,
    getters: true
  },
  toObject: {
    virtuals: false,
    getters: true
  },
  timestamps: true,
  strict: false
})
export class Asset {
  @Prop({ required: true, type: MongooseSchema.Types.ObjectId })
  organization_id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, type: [MongooseSchema.Types.ObjectId] })
  role_ids: MongooseSchema.Types.ObjectId[];

  @Prop({ required: false, type: [MongooseSchema.Types.ObjectId] })
  badge_ids: MongooseSchema.Types.ObjectId[];

  @Prop({ required: false, type: [MongooseSchema.Types.ObjectId] })
  program_ids: MongooseSchema.Types.ObjectId[];

  @Prop({ required: false, type: Number, default: 0 })
  delete_flag: number;

  @Prop({ required: true, type: String })
  id:string;

  [key: string]: any;
}

export const AssetSchema = SchemaFactory.createForClass(Asset);


AssetSchema.methods.toJSON = function () {
  const obj = this.toObject();
  obj.created_at = obj.createdAt || null;
  obj.updated_at = obj.updatedAt || null;

  delete obj.updatedAt;
  delete obj.createdAt;
  return obj;
};