import { Test, TestingModule } from '@nestjs/testing';
import { EntityPortfolioController } from './entity-portfolio.controller';
import { EntityPortfolioService } from './entity-portfolio.service';
import { getModelToken } from '@nestjs/mongoose';
import { EntityAssignment } from '../entity-assignment/schema/entity-assignment.schema';
import { EntityPortfolio } from './schema/entity-portfolo.schema';
import { ErrorService } from '../error/error.service';

describe('EntityPortfolioController', () => {
  let controller: EntityPortfolioController;
  let entityPortfolioService: EntityPortfolioService;

  const mockUserId = 'user-1';
  const mockRoleId = '507f1f77bcf86cd799439011';
  const mockOrgId = 'org-1';
  const mockProgramId = '507f1f77bcf86cd799439012';
  const mockLastSyncTime = '2025-06-27T09:19:23.000Z';

  const mockPortfolioResponse = {
    data: [{
      id: mockUserId,
      portfolio: [{ id: 'asset-1' }]
    }],
    success: true
  };

  const mockEntityPortfolioService = {
    getStaffPortfolioByAssignment: jest.fn().mockResolvedValue(mockPortfolioResponse),
    getStaffPortfolio: jest.fn().mockResolvedValue(mockPortfolioResponse)
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EntityPortfolioController],
      providers: [
        {
          provide: EntityPortfolioService,
          useValue: mockEntityPortfolioService,
        },
        ErrorService,
        {
          provide: getModelToken(EntityAssignment.name),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: getModelToken(EntityPortfolio.name),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<EntityPortfolioController>(EntityPortfolioController);
    entityPortfolioService = module.get<EntityPortfolioService>(EntityPortfolioService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getEntityPortfolio', () => {
    it('should get portfolio by assignment with all parameters', async () => {
      const result = await controller.getEntityPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId,
        mockLastSyncTime
      );

      expect(result).toEqual(mockPortfolioResponse);
      expect(entityPortfolioService.getStaffPortfolioByAssignment).toHaveBeenCalledWith(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId,
        mockLastSyncTime
      );
    });

    it('should get portfolio by assignment without last_sync_time', async () => {
      const result = await controller.getEntityPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      );

      expect(result).toEqual(mockPortfolioResponse);
      expect(entityPortfolioService.getStaffPortfolioByAssignment).toHaveBeenCalledWith(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId,
        undefined
      );
    });

    it('should handle service errors', async () => {
      const errorMessage = 'Service error';
      mockEntityPortfolioService.getStaffPortfolioByAssignment.mockRejectedValueOnce(new Error(errorMessage));

      await expect(controller.getEntityPortfolio(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId
      )).rejects.toThrow(errorMessage);
    });
  });

  describe('getEntityAssignment', () => {
    it('should get staff portfolio with all parameters', async () => {
      const result = await controller.getEntityAssignment(
        mockUserId,
        mockOrgId,
        mockRoleId,
        mockProgramId,
        mockLastSyncTime
      );

      expect(result).toEqual(mockPortfolioResponse);
      expect(entityPortfolioService.getStaffPortfolio).toHaveBeenCalledWith(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId,
        mockLastSyncTime
      );
    });

    it('should get staff portfolio without last_sync_time', async () => {
      const result = await controller.getEntityAssignment(
        mockUserId,
        mockOrgId,
        mockRoleId,
        mockProgramId
      );

      expect(result).toEqual(mockPortfolioResponse);
      expect(entityPortfolioService.getStaffPortfolio).toHaveBeenCalledWith(
        mockUserId,
        mockRoleId,
        mockOrgId,
        mockProgramId,
        undefined
      );
    });

    it('should handle service errors', async () => {
      const errorMessage = 'Service error';
      mockEntityPortfolioService.getStaffPortfolio.mockRejectedValueOnce(new Error(errorMessage));

      await expect(controller.getEntityAssignment(
        mockUserId,
        mockOrgId,
        mockRoleId,
        mockProgramId
      )).rejects.toThrow(errorMessage);
    });
  });
});
