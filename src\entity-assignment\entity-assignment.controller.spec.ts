import { Test, TestingModule } from '@nestjs/testing';
import { EntityAssignmentController } from './entity-assignment.controller';
import { EntityAssignmentService } from './entity-assignment.service';
import { CreateEntityAssignmentDto } from './dto/entity-assignment.dto';
import { Types } from 'mongoose';
import { EntityType } from './enum/entity-assignment.enum';

describe('EntityAssignmentController', () => {
  let controller: EntityAssignmentController;
  let mockEntityAssignmentService: any;

  const mockAssignment = {
    organization_id: new Types.ObjectId(),
    assigned_role_id: 'test-role-id',
    assigned_id: 'test-assigned-id',
    assigned_to_id: 'test-assigned-to-id',
    assigned_to_role_id: 'test-role-id',
    program_id: new Types.ObjectId(),
    assigned_to_entity_type: EntityType.USER,
    delete_flag: 0
  };

  beforeEach(async () => {
    mockEntityAssignmentService = {
      createEntityAssignment: jest.fn(),
      getAssignmentsByProgram: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [EntityAssignmentController],
      providers: [
        {
          provide: EntityAssignmentService,
          useValue: mockEntityAssignmentService,
        },
      ],
    }).compile();

    controller = module.get<EntityAssignmentController>(EntityAssignmentController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createEntityAssignment', () => {
    it('should create an entity assignment successfully', async () => {
      const payload: CreateEntityAssignmentDto = mockAssignment;
      const expectedResponse = {
        message: 'Entity assignment created successfully',
        status: 'success',
        data: { ...mockAssignment, id: new Types.ObjectId().toString() }
      };

      mockEntityAssignmentService.createEntityAssignment.mockResolvedValueOnce(expectedResponse);

      const result = await controller.createEntityAssignment(payload);

      expect(result).toEqual(expectedResponse);
      expect(mockEntityAssignmentService.createEntityAssignment).toHaveBeenCalledWith(payload);
    });

    it('should handle errors during creation', async () => {
      const payload: CreateEntityAssignmentDto = mockAssignment;
      const error = new Error('Creation failed');

      mockEntityAssignmentService.createEntityAssignment.mockRejectedValueOnce(error);

      await expect(controller.createEntityAssignment(payload)).rejects.toThrow(error);
    });
  });

  describe('getAssignmentsByProgram', () => {
    const mockProgramId = new Types.ObjectId();
    const mockLastSyncTime = '2023-01-01';

    it('should get assignments by program successfully', async () => {
      const expectedResponse = {
        message: 'Assignments gotten successfully',
        status: 'success',
        data: [mockAssignment]
      };

      mockEntityAssignmentService.getAssignmentsByProgram.mockResolvedValueOnce(expectedResponse);

      const result = await controller.getAssignmentsByProgram(mockProgramId.toString(), mockLastSyncTime);

      expect(result).toEqual(expectedResponse);
      expect(mockEntityAssignmentService.getAssignmentsByProgram).toHaveBeenCalledWith(
        mockProgramId.toString(),
        mockLastSyncTime
      );
    });

    it('should get assignments without last_sync_time', async () => {
      const expectedResponse = {
        message: 'Assignments gotten successfully',
        status: 'success',
        data: [mockAssignment]
      };

      mockEntityAssignmentService.getAssignmentsByProgram.mockResolvedValueOnce(expectedResponse);

      const result = await controller.getAssignmentsByProgram(mockProgramId.toString());

      expect(result).toEqual(expectedResponse);
      expect(mockEntityAssignmentService.getAssignmentsByProgram).toHaveBeenCalledWith(
        mockProgramId.toString(),
        undefined
      );
    });

    it('should handle errors during assignment retrieval', async () => {
      const error = new Error('Retrieval failed');

      mockEntityAssignmentService.getAssignmentsByProgram.mockRejectedValueOnce(error);

      await expect(controller.getAssignmentsByProgram(mockProgramId.toString())).rejects.toThrow(error);
    });
  });
});
