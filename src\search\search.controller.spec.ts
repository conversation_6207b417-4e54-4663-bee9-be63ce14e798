import { Test, TestingModule } from '@nestjs/testing';
import { SearchController } from './search.controller';
import { SearchService } from './search.service';
import { ErrorService } from '../error/error.service';

describe('SearchController', () => {
  let controller: SearchController;
  let service: SearchService;
  let mockSearchService: { search: jest.Mock };
  let mockErrorService: jest.Mocked<ErrorService>;

  beforeEach(async () => {
    mockSearchService = {
      search: jest.fn(),
    };

    mockErrorService = {
      badRequestError: jest.fn(),
      serverError: jest.fn(),
      notFoundError: jest.fn()
    } as jest.Mocked<ErrorService>;

    const module: TestingModule = await Test.createTestingModule({
      controllers: [SearchController],
      providers: [
        { provide: SearchService, useValue: mockSearchService },
        { provide: ErrorService, useValue: mockErrorService },
      ],
    }).compile();

    controller = module.get<SearchController>(SearchController);
    service = module.get<SearchService>(SearchService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('executeQuery', () => {
    it('should successfully execute a query', async () => {
      const mockQueryParams = { 
        resource: 'users',
        query: encodeURIComponent(JSON.stringify({ field: 'value' }))
      };
      const expectedResults = [{ id: 1, name: 'Test' }];
      mockSearchService.search.mockResolvedValueOnce(expectedResults);

      const result = await controller.executeQuery(mockQueryParams);

      expect(mockSearchService.search).toHaveBeenCalledWith(
        'users',
        JSON.stringify({ field: 'value' })
      );
      expect(result).toEqual(expectedResults);
    });

    it('should handle error when query fails', async () => {
      const mockQueryParams = { 
        resource: 'users',
        query: encodeURIComponent(JSON.stringify({ field: 'value' }))
      };
      mockSearchService.search.mockRejectedValueOnce(new Error('DB failed'));

      await expect(controller.executeQuery(mockQueryParams)).rejects.toThrow('DB failed');
    });

    it('should handle malformed query parameter', async () => {
      const mockQueryParams = { 
        resource: 'users',
        query: 'invalid-json'
      };

      const result = await controller.executeQuery(mockQueryParams);
      expect(result).toEqual([]);
      expect(mockErrorService.badRequestError).toHaveBeenCalledWith('Invalid JSON in query parameter');
    });
  });

  
});
