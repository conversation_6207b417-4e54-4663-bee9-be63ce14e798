import { Body, Controller, Post, Param, Get, Headers, Query } from '@nestjs/common';
import { CreateAssetDto } from './dto/asset.dto';
import { ApiBody, ApiOperation, ApiParam, ApiHeader, ApiQuery } from '@nestjs/swagger';
import { AssetService } from './asset.service';

@Controller('asset')
export class AssetController {
  constructor(private readonly assetService: AssetService) {}

  @ApiBody({
    type: CreateAssetDto,
    description: 'Details used to create a new asset',
  })
  @Post()
  async createAsset(@Body() payload: CreateAssetDto[]) {
    return this.assetService.createAsset(payload);
  }

  @Get('/organization/:organization_id')
  @ApiParam({
    name: 'organization_id',
    description: 'ID of the organization',
    required: true,
  })
  @ApiQuery({
    name: 'last_sync_time',
    description: 'Last sync time',
    required: false,
  })
  async getAllAssets(@Param('organization_id') organization_id: string, @Query('last_sync_time') last_sync_time?: string) {
    return this.assetService.getAllAssets(organization_id, last_sync_time);
  }
  @Get('/by-assignment')
  @ApiOperation({
    summary: 'Get assigned users',
  })
  @ApiHeader({
    name: 'user-id',
    description: 'ID of the user',
    required: true,
  })
  @ApiHeader({
    name: 'tenant-id',
    description: 'ID of the tenant',
    required: true,
  })
  @ApiQuery({
    name: 'role_id',
    description: 'ID of the role',
    required: true,
  })
  @ApiQuery({
    name: 'program_id',
    description: 'ID of the program',
    required: true,
  })
  @ApiQuery({
    name: 'last_sync_time',
    description: 'Last sync time',
    required: false,
  })
    async getAssignedUser(@Headers('user-id') user_id: string, @Headers('tenant-id') tenant_id: string, @Query('role_id') role_id: string, @Query('program_id') program_id: string, @Query('last_sync_time') last_sync_time?: string) {
      return this.assetService.getAssetAssignment(user_id,tenant_id,role_id,program_id,last_sync_time);
    }

    @Get('/migrate')
    @ApiOperation({
      summary: 'Get all assets',
    })
    async fetchAll() {
      return await this.assetService.fetchAll();
    }
  @Get('/:id')
  @ApiParam({
    name: 'id',
    description: 'ID of the asset',
    required: true,
  })
  async getAssetById(@Param('id') id: string) {
    return this.assetService.getAssetById(id);
  }

  // @Get('/assigned/:id')
  // @ApiParam({
  //   name: 'id',
  //   description: 'ID of the asset',
  //   required: true,
  // })
  // async getAssetAssignment(@Param('id') id: string) {
  //   return this.assetService.getAssetAssignment(id);
  // }



  
}
