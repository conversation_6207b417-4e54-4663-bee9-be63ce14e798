import { Controller, Get, Post, Query, Headers } from '@nestjs/common';
import { EntityAssignmentService } from './entity-assignment.service';
import { CreateEntityAssignmentDto } from './dto/entity-assignment.dto';
import { Body } from '@nestjs/common';
import { ApiOperation, ApiHeader, ApiQuery } from '@nestjs/swagger';

@Controller('entity-assignment')
export class EntityAssignmentController {
    constructor(
        private readonly entityAssignmentService: EntityAssignmentService
    ) {}
    @Post()
    async createEntityAssignment(@Body() payload: CreateEntityAssignmentDto): Promise<any> {
        return this.entityAssignmentService.createEntityAssignment(payload);
    }

  @Get('')
  @ApiQuery({
    name: 'program_id',
    description: 'ID of the program',
    required: true,
  })
  @ApiQuery({
    name: 'last_sync_time',
    description: 'Last sync time',
    required: false,
  })
  async getAssignmentsByProgram(@Query('program_id') program_id: string, @Query('last_sync_time') last_sync_time?: string) {
    return this.entityAssignmentService.getAssignmentsByProgram(program_id, last_sync_time);
  }
}
