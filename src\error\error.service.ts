import { HttpException, HttpStatus, Injectable } from '@nestjs/common';

@Injectable()
export class ErrorService {
  serverError(err: string) {
    throw new HttpException(
      {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        error: err,
      },
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
  badRequestError(err: string) {
    throw new HttpException(
      {
        status: HttpStatus.BAD_REQUEST,
        error: err,
      },
      HttpStatus.BAD_REQUEST,
    );
  }
  notFoundError(err: string) {
    throw new HttpException(
      {
        status: HttpStatus.NOT_FOUND,
        error: err,
      },
      HttpStatus.NOT_FOUND,
    );
  }
}
