import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EntityAssignmentController } from './entity-assignment.controller';
import { EntityAssignmentService } from './entity-assignment.service';
import { EntityAssignment, EntityAssignmentSchema } from './schema/entity-assignment.schema';
import { ErrorService } from '../error/error.service';
import { UtilsModule } from '../utils/utils.module';
import { EntityPortfolioService } from '../entity-portfolio/entity-portfolio.service';
import { EntityPortfolioModule } from '../entity-portfolio/entity-portfolio.module';
import { EntityPortfolio, EntityPortfolioSchema } from '../entity-portfolio/schema/entity-portfolo.schema';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: EntityAssignment.name, schema: EntityAssignmentSchema },
      { name: EntityPortfolio.name, schema: EntityPortfolioSchema },
    ]),
    UtilsModule,
    EntityPortfolioModule
  ],
  controllers: [EntityAssignmentController],
  providers: [EntityAssignmentService, ErrorService, EntityPortfolioService],
  exports: [EntityAssignmentService]
})
export class EntityAssignmentModule {}
