import { Types } from 'mongoose';
import { CreateUserDto, ScreenDto } from '../dto/users.dto';
import { BadgeDto } from '../dto/badge.dto';

// 683dad0d6d0c08456b43bbaa
// export const payload: CreateUserDto = {
//   organization_id: new Types.ObjectId('507f1f77bcf86cd799439011'),
//   role: new Types.ObjectId('507f1f77bcf86cd799439012'),
//   badge_ids: [new Types.ObjectId('507f1f77bcf86cd799439013')],
//   response: [
//     {
//       id: 'screen-1',
//       sections: [
//         {
//           id: 'section-1',
//           components: [
//             { id: 'component-1', label: 'name', answer: 'Entity1' },
//             { id: 'component-2', label: 'value', answer: 'Value1' },
//           ],
//         },
//       ],
//     },
//   ],
//   entity: [new Types.ObjectId('507f1f77bcf86cd799439013')],
//   program_ids: [new Types.ObjectId('507f1f77bcf86cd799439013')],
//   unique_entity_id: '507f1f77bcf86cd799439013',
//   response_id: '507f1f77bcf86cd799439013',
//   target_id: '507f1f77bcf86cd799439013',
//   performer_id: '507f1f77bcf86cd799439013',
//   home_office: {
//     id: '683db01d6d0c08456b43be4d',
//     code: 'FAHUB-NG-0101001',
//     name: 'Hub Alpha',
//     level: 'hub'
//   },
// };

export const mockHomeOffice = {
  id: "683db01d6d0c08456b43be4d",
  code: "FAHUB-NG-0101001",
  name: "Hub Alpha",
  level: "hub"
};

export const mockScreen: ScreenDto = {
  id: 'screen_131160',
  sections: []
};

export const mockProgramId = '6862472a1bd11619481752b7';
export const mockOrgId = '6862472a1bd11619481752b6';
export const mockAssets = [
  { id: 'asset1', name: 'Asset 1' },
  { id: 'asset2', name: 'Asset 2' }
];

export const mockPayload: CreateUserDto = {
  organization_id: new Types.ObjectId(mockOrgId),
  entity: [new Types.ObjectId('6862472a1bd11619481752b5')],
  program_ids: [new Types.ObjectId(mockProgramId)],
  performer_id: 'test-performer-id',
  role: new Types.ObjectId('6862472a1bd11619481752b8'),
  unique_entity_id: 'test-unique-id',
  response: [mockScreen],
  response_id: 'test-response-id',
  target_id: 'test-target-id',
  home_office: {
    id: '683db01d6d0c08456b43be4d',
    code: 'FAHUB-NG-0101001',
    name: 'Hub Alpha',
    level: 'hub',
  }
};

export const mockTransformedPayload = {
  ...mockPayload,
  id: 'test-id',
  delete_flag: 0,
  created_at: expect.any(Date),
  updated_at: expect.any(Date)
};
export const mockTransformedPayloadUpdate = {
  ...mockPayload,
  delete_flag: 0,
  created_at: expect.any(Date),
  updated_at: expect.any(Date)
};

export const mockUsers = [
  {
    id: 'user1',
    tenantId: new Types.ObjectId('507f1f77bcf86cd799439011'),
    updated_at: new Date('2025-06-30T10:00:00Z'),
    program: [
      {
        role: { id: 'role1' },
        programId: 'program1'
      }
    ]
  },
  {
    id: 'user2',
    tenantId: new Types.ObjectId('507f1f77bcf86cd799439012'),
    updated_at: new Date('2025-06-30T09:00:00Z'),
    program: []
  }
];

export const mockBadgePayload: BadgeDto = {
  id: 'test-user-id',
  organization_id: new Types.ObjectId('507f1f77bcf86cd799439011'),
  badge_id: [new Types.ObjectId('507f1f77bcf86cd799439012')]
};
export const mockEvent = {
  type: 'badge_assignment',
  data: {
    user_id: 'test-user-id',
    badge_id: 'test-badge-id'
  }
};