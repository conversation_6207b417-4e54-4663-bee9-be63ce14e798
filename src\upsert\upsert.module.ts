import { Modu<PERSON> } from '@nestjs/common';
import { UpsertService } from './upsert.service';
import { UpsertController } from './upsert.controller';
import { ErrorModule } from '../error/error.module';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    ErrorModule,
    UsersModule,
    MongooseModule.forFeature([])
  ],
  controllers: [UpsertController],
  providers: [UpsertService]
})
export class UpsertModule {}
