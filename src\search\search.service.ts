import { Injectable } from '@nestjs/common';
import { ErrorService } from '../error/error.service';
import { Connection, Types } from 'mongoose';

@Injectable()
export class SearchService {
  constructor(
    private readonly errorService: ErrorService,
    private readonly connection: Connection,
  ) {}

  public async search(resource: string, query: string): Promise<any> {
    try {

      const collection = this.connection.collection(resource);

      const parsedQuery = JSON.parse(query);
      
      let results = await collection.find(parsedQuery).toArray();
      
      if (results.length === 0) {
        const objectIdQuery = this.tryConvertToObjectId(parsedQuery);
        if (objectIdQuery !== parsedQuery) {
          results = await collection.find(objectIdQuery).toArray();
        }
      }
      return {
        message: 'users gotten successfully',
        status: 'success',
        data: results,
      };
    } catch (err) {
      this.errorService.serverError(`Error while searching: ${err.message}`);
      return [];
    }
  }

  private tryConvertToObjectId(query: any): any {
    if (typeof query !== 'object' || query === null) return query;

    const result: any = Array.isArray(query) ? [] : {};

    for (const key in query) {
      const value = query[key];

      if (typeof value === 'string' && /^[0-9a-fA-F]{24}$/.test(value)) {
        try {
          result[key] = new Types.ObjectId(value);
        } catch {
          result[key] = value;
        }
      } else if (typeof value === 'object' && value !== null) {
        result[key] = this.tryConvertToObjectId(value);
      } else {
        result[key] = value;
      }
    }

    return result;
  }
}
