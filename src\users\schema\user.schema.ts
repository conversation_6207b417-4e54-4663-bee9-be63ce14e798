import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type UserDocument = User & Document & {
  [key: string]: any;
};

@Schema({
  toJSON: {
    virtuals: false,
    getters: true
  },
  toObject: {
    virtuals: false,
    getters: true
  },
  timestamps: true,
  strict: false,
})
export class User {
  @Prop({ required: true, type: MongooseSchema.Types.ObjectId })
  organization_id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, type: [MongooseSchema.Types.ObjectId] })
  role_ids: MongooseSchema.Types.ObjectId[];

  @Prop({ required: false, type: [MongooseSchema.Types.ObjectId] })
  badge_ids: MongooseSchema.Types.ObjectId[];

  @Prop({ required: true, type: String, unique: true })
  id: string;

  @Prop({ required: true, type: [MongooseSchema.Types.ObjectId] })
  program_ids: MongooseSchema.Types.ObjectId[];

  @Prop({ required: false, type: Number, default: 0 })
  delete_flag: number;

  [key: string]: any;
}

export const UserSchema = SchemaFactory.createForClass(User);


UserSchema.methods.toJSON = function () {
  const obj = this.toObject();
  obj.created_at = obj.createdAt || null;
  obj.updated_at = obj.updatedAt || null;

  delete obj.updatedAt;
  delete obj.createdAt;
  return obj;
};