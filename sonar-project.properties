sonar.projectKey=agrios-25-entity-service

# relative paths to source directories. More details and properties are described
# at https://docs.sonarqube.org/latest/project-administration/narrowing-the-focus/ 
sonar.javascript.lcov.reportPaths=./coverage/lcov.info
sonar.sources=src
sonar.tests=test

sonar.test.inclusions=**/*.spec.ts
# Exclude from coverage
sonar.coverage.exclusions=**/*.spec.ts,**/*.module.ts,**/*.dto.ts,**/*.enum.ts,**/*.interface.ts,**/*.schema.ts,**/*mock*/**,**/app.controller.ts,**/app.service.ts,**/main.ts,**/task-schedule/**

# Exclude from analysis 
sonar.exclusions=**/*.spec.ts,**/*.module.ts,**/*.dto.ts,**/*.enum.ts,**/*.interface.ts,**/*.schema.ts,**/*mock*/**,**/app.controller.ts,**/app.service.ts,**/main.ts,**/task-schedule/**