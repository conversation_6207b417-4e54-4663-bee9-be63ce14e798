import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as Joi from 'joi';
import { MongooseModule } from '@nestjs/mongoose';
import { AssetModule } from './asset/asset.module';
import { ErrorModule } from './error/error.module';
import { SearchModule } from './search/search.module';
import { EntityAssignmentModule } from './entity-assignment/entity-assignment.module';
import { UtilsModule } from './utils/utils.module';
import { UpsertModule } from './upsert/upsert.module';
import { EntityPortfolioModule } from './entity-portfolio/entity-portfolio.module';


@Module({
  imports: [
    ScheduleModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        MONGO_URL: Joi.string().required(),
        PORT: Joi.number().port().default(3000),
      }),
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGO_URL'),
      }),
      inject: [ConfigService],
    }),
    UsersModule,
    AssetModule,
    ErrorModule,
    SearchModule,
    EntityAssignmentModule,
    UtilsModule,
    UpsertModule,
    EntityPortfolioModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
