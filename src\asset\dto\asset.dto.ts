import {
  Is<PERSON>rray,
  IsMongoId,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { Type } from 'class-transformer';

export class ComponentDto {
  @ApiProperty({
    description: 'Component ID',
    example: 'single_choice_747081',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Component label',
    example: 'Select Role',
  })
  @IsString()
  @IsOptional()
  label?: string;

  @ApiProperty({
    description: 'Component tag',
    example: 'select_role',
  })
  @IsString()
  @IsOptional()
  tag?: string;
  
  @ApiProperty({
    description: 'Component answer',
    example: 'Prospective Entrepreneur',
  })
  @IsString()
  @IsNotEmpty()
  answer: string;
}

export class SectionDto {
  @ApiProperty({
    description: 'Section ID',
    example: 'section_479098',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Section components',
    type: [ComponentDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ComponentDto)
  components: ComponentDto[];
}

export class ScreenDto {
  @ApiProperty({
    description: 'Screen ID',
    example: 'screen_131160',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Screen sections',
    type: [SectionDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SectionDto)
  sections: SectionDto[];
}

export class CreateAssetDto {
  @ApiProperty({
    description: 'Organization ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsNotEmpty()
  @IsMongoId()
  organization_id: Types.ObjectId;

  @ApiProperty({
    description: 'Badges array',
    example: ['entrepreneur'],
  })
  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true })
  badge_ids?: Types.ObjectId[];

  @ApiProperty({
    description: 'Entity array',
    example: ['entrepreneur'],
  })
  @IsArray()
  @IsMongoId({ each: true })
  @IsNotEmpty()
  entity: Types.ObjectId[];

  @ApiProperty({
    description: 'Role',
    example: '507f1f77bcf86cd799439011',
  })
  @IsMongoId()
  role: Types.ObjectId;

  @ApiProperty({
    description: 'Programs array',
    example: ['507f1f77bcf86cd799439011'],
  })
  @IsArray()
  @IsMongoId({ each: true })
  program_ids: Types.ObjectId[];

  @ApiProperty({
    description: 'Unique Entity ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsOptional()
  @IsString()
  unique_entity_id?: string;

  @ApiProperty({
    description: 'Facial Template',
    example: 'facial template',
  })
  @IsString()
  @IsOptional()
  facial_template?: string;

  @ApiProperty({
    description: 'Response array containing screens',
    type: [ScreenDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ScreenDto)
  @IsNotEmpty()
  response: ScreenDto[];

  @ApiProperty({
    description: 'Response ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  @IsOptional()
  response_id?: string;

  @ApiProperty({
    description: 'Target ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  @IsOptional()
  target_id?: string;

  @ApiProperty({
    description: 'Performer ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  @IsOptional()
  performer_id?: string;
}

export class UpdateAssetDto {
  [key: string]: any;
}
