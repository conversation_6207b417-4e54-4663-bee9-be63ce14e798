import { Modu<PERSON> } from '@nestjs/common';
import { EntityPortfolioService } from './entity-portfolio.service';
import { EntityPortfolioController } from './entity-portfolio.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { EntityPortfolio, EntityPortfolioSchema } from './schema/entity-portfolo.schema';
import { EntityAssignment, EntityAssignmentSchema } from '../entity-assignment/schema/entity-assignment.schema';
import { UtilsModule } from 'src/utils/utils.module';
import { ErrorModule } from '../error/error.module';

@Module({
    imports: [
      MongooseModule.forFeature([
        { name: EntityPortfolio.name, schema: EntityPortfolioSchema },
        { name: EntityAssignment.name, schema: EntityAssignmentSchema },
      ]),
      UtilsModule,
      ErrorModule
    ],
  controllers: [EntityPortfolioController],
  providers: [EntityPortfolioService],
  exports: [EntityPortfolioService]
})
export class EntityPortfolioModule {}
