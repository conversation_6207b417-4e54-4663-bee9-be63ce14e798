name: Build and Deploy to Google Cloud Run

on:
  push:
    branches:
      - staging

env:
  WIF_PROVIDER: ${{ secrets.WIF_PROVIDER }}
  SA_EMAIL: ${{ secrets.SA_EMAIL }}
  PROJECT_ID: ${{ secrets.GKE_PROJECT }}
  REGION: ${{ vars.REGION }}
  SERVICE: ${{ vars.SERVICE_NAME }}
  REPOSITORY: ${{ vars.ARTIFACT_REPO }}
  GITHUB_SHA: ${{ github.sha }}

jobs:
  build-and-push:
    name: Build and Push Image to Artifact Repo
    runs-on: ubuntu-latest
    continue-on-error: false
    environment: staging
    permissions:
      contents: 'read'
      id-token: 'write'
    outputs:
      short_sha: ${{ steps.truncate_sha.outputs.short_sha }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        id: gcp-auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ env.WIF_PROVIDER }}
          service_account: ${{ env.SA_EMAIL }}

      - name: Configure Docker to use gcloud as a credential helper
        run: |
          gcloud auth configure-docker $REGION-docker.pkg.dev --quiet

      - name: Truncate SHA
        id: truncate_sha
        run: echo "short_sha=${GITHUB_SHA:0:7}" >> $GITHUB_OUTPUT

      - name: Build Docker Image
        run: |
          docker build -t ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE }}:${{ steps.truncate_sha.outputs.short_sha }} .
        
      - name: Publish
        run: |
          docker push ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE }}:${{ steps.truncate_sha.outputs.short_sha }}

      - name: Slack Notification
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: devops-app-notification
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_LINK_NAMES: true
          SLACK_MSG_AUTHOR: ${{ github.actor }}
          SLACK_TITLE: AgricOS-v25 Entity Service
          SLACK_MESSAGE: "Trivy scan failed for Commit - ${{ github.event.head_commit.message }}"
          SLACK_USERNAME: "rtCamp"
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

  dev-deploy:
    name: Deploy image to Cloud run
    runs-on: ubuntu-latest
    needs: [build-and-push]
    environment: staging
    permissions:
      contents: 'read'
      id-token: 'write'
    steps:
      - name: Authenticate to Google Cloud
        id: gcp-auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ env.WIF_PROVIDER }}
          service_account: ${{ env.SA_EMAIL }}

      - name: Deploy to Cloud Run
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: ${{ env.SERVICE }}
          region: ${{ env.REGION }}
          image: ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE }}:${{ needs.build-and-push.outputs.short_sha }}
        id: deploy

      - name: Use output
        run: curl "${{ steps.deploy.outputs.url }}"

      - name: Slack Notification
        if: always()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_CHANNEL: devops-app-notification
          SLACK_COLOR: ${{ job.status }}
          SLACK_ICON: https://github.com/rtCamp.png?size=48
          SLACK_LINK_NAMES: true
          SLACK_MSG_AUTHOR: ${{ github.actor }}
          SLACK_TITLE: Entity Service (Staging)
          SLACK_MESSAGE: "Cloud Run deployment ${{ job.status }} for Commit - ${{ github.event.head_commit.message }}"
          SLACK_USERNAME: "rtCamp"
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}