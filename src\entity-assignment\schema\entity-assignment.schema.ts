import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { EntityType } from '../enum/entity-assignment.enum';

export type EntityAssignmentDocument = EntityAssignment & Document & {
  id: string;
  [key: string]: any;
};

@Schema({
  toJSON: {
    virtuals: false,
    getters: true
  },
  toObject: {
    virtuals: false,
    getters: true
  },
  timestamps: true,
  strict: true
})
export class EntityAssignment {
  @Prop({ required: true, type: MongooseSchema.Types.ObjectId })
  organization_id: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, type: MongooseSchema.Types.ObjectId })
  assigned_role_id: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, type: String })
  assigned_id: string;

  @Prop({ required: true, type: String })
  assigned_to_id: string;

  @Prop({ required: true, type: MongooseSchema.Types.ObjectId })
  assigned_to_role_id: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, type: MongooseSchema.Types.ObjectId })
  program_id: MongooseSchema.Types.ObjectId;

  @Prop({ required: false, type: String, enum: EntityType })
  assigned_to_entity_type: EntityType;

  @Prop({ required: false, type: Number, default: 0 })
  delete_flag: number;
}

export const EntityAssignmentSchema = SchemaFactory.createForClass(EntityAssignment);


EntityAssignmentSchema.methods.toJSON = function () {
  const obj = this.toObject();
  obj.created_at = obj.createdAt || null;
  obj.updated_at = obj.updatedAt || null;
  obj.id = obj._id.toString();

  delete obj.updatedAt;
  delete obj.createdAt;
  delete obj._id;
  return obj;
};