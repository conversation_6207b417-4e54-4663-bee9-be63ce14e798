import {
  <PERSON>,
  Get,
  Post,
  Query,
  Headers,
  Param,
  Body,
} from '@nestjs/common';
import { UsersService } from './users.service';
import {
  CreateUserDto,
  RequestParticipantsDTO,
  RequestRegisteredDTO,
} from './dto/users.dto';
import {
  <PERSON>pi<PERSON>ody,
  ApiOperation,
  ApiParam,
  ApiHeader,
  ApiQuery,
} from '@nestjs/swagger';
import { ErrorService } from '../error/error.service';
import { ValidateArrayPipe } from '../utils/validate-array.pipe';
import { Types } from 'mongoose';
import { BadgeDto } from './dto/badge.dto';

@Controller('user')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly errorService: ErrorService,
  ) {}

  @ApiBody({
    type: CreateUserDto,
    description: 'Details used to create a new user',
  })
  @Post()
  async createUser(@Body() payload: CreateUserDto[]) {
    return this.usersService.createUser(payload);
  }

  @Get('/organization/:organization_id')
  @ApiParam({
    name: 'organization_id',
    description: 'ID of the organization',
    required: true,
  })
  @ApiQuery({
    name: 'last_sync_time',
    description: 'Last sync time',
    required: false,
  })
  async getAllUsers(
    @Param('organization_id') organization_id: string,
    @Query('last_sync_time') last_sync_time?: string,
  ) {
    return this.usersService.getAllUsers(organization_id, last_sync_time);
  }

  @Get('/by-assignment')
  @ApiOperation({
    summary: 'Get assigned users',
  })
  @ApiHeader({
    name: 'user-id',
    description: 'ID of the user',
    required: true,
  })
  @ApiHeader({
    name: 'tenant-id',
    description: 'ID of the tenant',
    required: true,
  })
  @ApiQuery({
    name: 'program_id',
    description: 'ID of the program',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'organization_id',
    description: 'ID of the organization',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'role_id',
    description: 'ID of the role',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'last_sync_time',
    description: 'Last sync time',
    required: false,
    type: String,
  })
  async getAssignedUser(
    @Headers('user-id') user_id: string,
    @Headers('tenant-id') tenant_id: string,
    @Query('role_id') role_id: string,
    @Query('program_id') program_id: string,
    @Query('last_sync_time') last_sync_time?: string,
  ) {
    return this.usersService.getUserAssignment(
      user_id,
      tenant_id,
      role_id,
      program_id,
      last_sync_time,
    );
  }
  @Get('/migrate')
  @ApiOperation({
    summary: 'Get all users',
  })
  async fetchAll() {
    return await this.usersService.fetchAll();
  }
  @Get('/program-entities')
  @ApiOperation({
    summary: 'Get entities within a program',
  })
  @ApiQuery({ name: 'program_id', required: true })
  @ApiQuery({ name: 'organization_id', required: true })
  async getProgramEntities(
    @Query('program_id') program_id: string,
    @Query('organization_id') organization_id: string,
  ) {
    return await this.usersService.getProgramEntities(
      program_id,
      organization_id,
    );
  }

  @Post('/update-badge')
  async upsertBadge(@Body() payload: BadgeDto) {
    return this.usersService.upsertBadge(payload);
  }

  @Get('/:id')
  @ApiParam({
    name: 'id',
    description: 'ID of the user',
    required: true,
  })
  @ApiQuery({
    name: 'last_sync_time',
    description: 'Last sync time',
    required: false,
  })
  async getUserById(
    @Param('id') id: string,
    @Query('last_sync_time') last_sync_time?: string,
  ) {
    return this.usersService.getUserById(id, last_sync_time);
  }

  @Post('/participants')
  async getParticipantUserIds(
    @Body(new ValidateArrayPipe(RequestParticipantsDTO))
    payload: RequestParticipantsDTO[],
  ) {
    return this.usersService.getProgramParticipants(payload);
  }

  @Post('/registered')
  async getRegisteredUserIds(
    @Body(new ValidateArrayPipe(RequestRegisteredDTO))
    payload: RequestRegisteredDTO[],
  ) {
    return this.usersService.getRegisteredIds(payload);
  }

 
}
